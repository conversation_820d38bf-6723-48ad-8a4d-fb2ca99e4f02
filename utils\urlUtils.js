/**
 * URL工具函数
 * 用于处理URL解析和参数提取
 */

/**
 * 从URL中提取查询参数
 * @param {string} url - 完整的URL
 * @param {string} paramName - 要提取的参数名
 * @returns {string|null} 参数值，如果不存在则返回null
 */
function getUrlParameter(url, paramName) {
  try {
    // 使用正则表达式匹配参数
    const regex = new RegExp(`[?&]${paramName}=([^&]+)`);
    const match = url.match(regex);
    
    if (match && match[1]) {
      return decodeURIComponent(match[1]);
    }
    
    return null;
  } catch (error) {
    console.error('解析URL参数失败:', error);
    return null;
  }
}

/**
 * 解析URL中的所有查询参数
 * @param {string} url - 完整的URL
 * @returns {Object} 包含所有参数的对象
 */
function parseUrlParameters(url) {
  const params = {};
  
  try {
    // 提取查询字符串部分
    const queryString = url.split('?')[1];
    
    if (!queryString) {
      return params;
    }
    
    // 分割参数
    const pairs = queryString.split('&');
    
    pairs.forEach(pair => {
      const [key, value] = pair.split('=');
      if (key && value) {
        params[decodeURIComponent(key)] = decodeURIComponent(value);
      }
    });
    
  } catch (error) {
    console.error('解析URL参数失败:', error);
  }
  
  return params;
}

/**
 * 验证URL格式
 * @param {string} url - 要验证的URL
 * @returns {boolean} 是否为有效的URL
 */
function isValidUrl(url) {
  try {
    return url && (url.startsWith('http://') || url.startsWith('https://'));
  } catch (error) {
    return false;
  }
}

/**
 * 从设备二维码URL中提取设备序列号
 * @param {string} url - 设备二维码URL
 * @returns {string|null} 设备序列号，如果提取失败则返回null
 */
function extractDeviceSnFromUrl(url) {
  try {
    console.log('=== 提取设备序列号 ===');
    console.log('URL:', url);
    
    // 验证URL格式
    if (!isValidUrl(url)) {
      console.error('无效的URL格式');
      return null;
    }
    
    // 提取sncode参数
    const sncode = getUrlParameter(url, 'sncode');
    
    if (!sncode) {
      console.error('URL中未找到sncode参数');
      return null;
    }
    
    // 验证序列号格式
    if (sncode.length < 8 || !/^[A-Za-z0-9]+$/.test(sncode)) {
      console.error('设备序列号格式不正确:', sncode);
      return null;
    }
    
    console.log('提取的设备序列号:', sncode);
    return sncode;
    
  } catch (error) {
    console.error('提取设备序列号失败:', error);
    return null;
  }
}

/**
 * 构建设备二维码URL
 * @param {string} baseUrl - 基础URL
 * @param {string} sncode - 设备序列号
 * @returns {string} 完整的设备二维码URL
 */
function buildDeviceQRCodeUrl(baseUrl, sncode) {
  try {
    const separator = baseUrl.includes('?') ? '&' : '?';
    return `${baseUrl}${separator}sncode=${encodeURIComponent(sncode)}`;
  } catch (error) {
    console.error('构建设备二维码URL失败:', error);
    return '';
  }
}

module.exports = {
  getUrlParameter,
  parseUrlParameters,
  isValidUrl,
  extractDeviceSnFromUrl,
  buildDeviceQRCodeUrl
};
