/* pages/device-naming/device-naming.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 设备ID显示 */
.device-id-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.device-id-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.device-id-value {
  font-size: 32rpx;
  color: #00C2B3;
  font-weight: 600;
  word-break: break-all;
}

/* 设备命名区域 */
.naming-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

/* 自定义输入 */
.input-section {
  margin-bottom: 40rpx;
}

.input-label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 600;
}

.device-name-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 30rpx;
  background: #f8f9fa;
  box-sizing: border-box;
}

.device-name-input:focus {
  border-color: #00C2B3;
  background: white;
}

/* 快捷命名 */
.quick-naming-section {
  /* 快捷命名样式 */
}

.quick-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.quick-names {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.quick-name-item {
  padding: 16rpx 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #495057;
  transition: all 0.2s;
}

.quick-name-item.selected {
  background: #00C2B3;
  border-color: #00C2B3;
  color: white;
}

.quick-name-item:active {
  transform: scale(0.95);
}

/* 分组选择 */
.group-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.group-label {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.add-group-btn {
  font-size: 28rpx;
  color: #00C2B3;
  padding: 8rpx 16rpx;
  border: 1rpx solid #00C2B3;
  border-radius: 20rpx;
}

.add-group-btn:active {
  background: #00C2B3;
  color: white;
}

.group-selector {
  /* 分组选择器样式 */
}

.group-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.group-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #00C2B3;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-check {
  font-size: 20rpx;
  color: white;
}

.icon-check.default-group {
  /* 默认分组特殊样式 */
}

.group-info {
  flex: 1;
}

.group-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

.group-desc {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

/* 设备二维码 */
.qrcode-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.qrcode-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  transition: background-color 0.2s;
}

.qrcode-item:active {
  background: #f8f9fa;
}

.qrcode-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-qr {
  font-size: 28rpx;
}

.qrcode-content {
  flex: 1;
}

.qrcode-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.qrcode-desc {
  font-size: 24rpx;
  color: #666;
}

.qrcode-action {
  /* 二维码操作样式 */
}

.action-text {
  font-size: 28rpx;
  color: #00C2B3;
  padding: 8rpx 16rpx;
  border: 1rpx solid #00C2B3;
  border-radius: 20rpx;
}

/* 保存按钮 */
.save-section {
  margin-top: 40rpx;
  padding: 0 20rpx;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #cccccc;
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-btn:not(.loading):not([disabled]) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.save-btn.loading {
  background: #cccccc;
}

.save-btn:active:not([disabled]) {
  transform: scale(0.98);
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  min-width: 200rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #00C2B3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
