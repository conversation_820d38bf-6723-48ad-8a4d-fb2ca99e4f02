.container {
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.filter-btn {
  padding: 10rpx;
}

.filter-icon {
  width: 40rpx;
  height: 40rpx;
}

.date-filter {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.date-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.date-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.date-value {
  font-size: 28rpx;
  color: #333;
}

.date-separator {
  width: 2rpx;
  height: 40rpx;
  background-color: #e0e0e0;
  margin: 0 30rpx;
}

.alarm-list {
  margin-bottom: 120rpx;
}

.date-group {
  margin-bottom: 30rpx;
}

.date-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.date-text {
  font-size: 28rpx;
  color: #666;
}

.count {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.alarm-item {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.alarm-time {
  width: 120rpx;
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}

.alarm-content {
  flex: 1;
}

.alarm-type {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.type-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.type-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.alarm-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.alarm-image {
  width: 200rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.no-data-img {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.no-data-text {
  font-size: 28rpx;
  color: #999;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.btn-clear-all {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #f5222d;
  color: #fff;
  font-size: 32rpx;
  border-radius: 40rpx;
}
