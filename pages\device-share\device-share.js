const app = getApp();

Page({
  data: {
    deviceId: '',
    device: {
      id: '',
      name: '',
      imageUrl: ''
    },
    sharedUsers: []
  },

  onLoad: function(options) {
    const deviceId = options.id;
    if (deviceId) {
      this.setData({
        deviceId: deviceId
      });
      this.getDeviceInfo(deviceId);
      this.getSharedUsers(deviceId);
    }
  },

  getDeviceInfo: function(deviceId) {
    // 这里应该调用 GET_DEVICE_DETAIL 接口获取设备信息
    // 现在暂时使用模拟数据
    wx.showLoading({
      title: '加载中',
    });
    
    setTimeout(() => {
      this.setData({
        device: {
          id: deviceId,
          name: '智能摄像头',
          imageUrl: ''
        }
      });
      wx.hideLoading();
    }, 500);
  },

  getSharedUsers: function(deviceId) {
    // 这里应该调用相关接口获取已分享用户列表
    // 现在暂时使用模拟数据
    setTimeout(() => {
      this.setData({
        sharedUsers: [
          {
            id: '1',
            name: '张三',
            avatar: '/assets/images/avatar1.png',
            shareTime: '2025-07-20 14:30'
          },
          {
            id: '2',
            name: '李四',
            avatar: '/assets/images/avatar2.png',
            shareTime: '2025-07-15 09:45'
          }
        ]
      });
    }, 500);
  },

  shareByQrCode: function() {
    // 这里应该调用接口生成设备分享二维码
    // 现在暂时使用模拟数据
    wx.showLoading({
      title: '生成二维码',
    });
    
    setTimeout(() => {
      wx.hideLoading();
      wx.navigateTo({
        url: '/pages/qrcode/qrcode?type=share&deviceId=' + this.data.deviceId
      });
    }, 1000);
  },

  shareByContact: function() {
    // 跳转到好友列表页面
    wx.navigateTo({
      url: '/pages/friends/friends?action=share&deviceId=' + this.data.deviceId
    });
  },

  cancelShare: function(e) {
    const userId = e.currentTarget.dataset.id;
    const userName = this.data.sharedUsers.find(user => user.id === userId).name;
    
    wx.showModal({
      title: '取消分享',
      content: `确定要取消与"${userName}"的分享吗？`,
      confirmColor: '#f5222d',
      success: (res) => {
        if (res.confirm) {
          this.doCancelShare(userId);
        }
      }
    });
  },

  doCancelShare: function(userId) {
    // 这里应该调用 CANCEL_SHARE_DEVICE 接口取消设备分享
    // 现在暂时使用模拟数据
    wx.showLoading({
      title: '取消分享',
    });
    
    setTimeout(() => {
      // 更新本地数据，移除已取消分享的用户
      const updatedUsers = this.data.sharedUsers.filter(user => user.id !== userId);
      this.setData({
        sharedUsers: updatedUsers
      });
      
      wx.hideLoading();
      wx.showToast({
        title: '已取消分享',
        icon: 'success'
      });
    }, 1000);
  },

  onPullDownRefresh: function() {
    this.getSharedUsers(this.data.deviceId);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 500);
  }
}); 