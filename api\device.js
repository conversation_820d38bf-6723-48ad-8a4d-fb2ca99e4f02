const { post } = require('../utils/request');
const { validateN<PERSON>berRang<PERSON>, validateRequiredFields } = require('../utils/validator');

/**
 * 查询用户设备列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNo - 当前页码，从1开始
 * @param {number} params.pageSize - 分页大小，大于等于1，小于等于100
 * @returns {Promise} Promise对象
 */
function queryUserDeviceList(params = {}) {
  // 参数验证
  const pageNo = params.pageNo || 1;
  const pageSize = params.pageSize || 10;
  
  if (!validateNumberRange(pageNo, 1, 10000)) {
    return Promise.reject({ code: 1012, msg: '页码必须为1-10000之间的正整数' });
  }

  if (!validateNumberRange(pageSize, 1, 100)) {
    return Promise.reject({ code: 1012, msg: '每页条数必须为1-100之间的正整数' });
  }

  const requestData = {
    Action: 'QUERY_USER_DEVICE_LIST',
    pageNo: pageNo,
    pageSize: pageSize
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 查询设备列表需要Token
  });
}

/**
 * 查询用户分组列表
 * @returns {Promise} Promise对象
 */
function queryUserGroup() {
  const requestData = {
    Action: 'QUERY_USER_GROUP'
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 查询分组列表需要Token
  });
}

/**
 * 用户绑定设备
 * @param {string} productKey - 产品ID
 * @param {string} deviceName - 设备名称
 * @param {string} nickName - 设备昵称(可选)
 * @param {number} groupId - 分组ID，默认分组传0
 * @returns {Promise} Promise对象
 */
function userBindDevice(productKey, deviceName, nickName = '', groupId = 0) {
  if (!productKey) {
    return Promise.reject({ code: 1012, msg: '产品ID不能为空' });
  }
  
  if (!deviceName) {
    return Promise.reject({ code: 1012, msg: '设备名称不能为空' });
  }
  
  const requestData = {
    Action: 'USER_BIND_DEVICE',
    productKey: productKey,
    deviceName: deviceName,
    nickName: nickName || deviceName,
    groupId: groupId
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 绑定设备需要Token
  });
}

/**
 * 用户解绑设备
 * @param {string} productKey - 产品ID
 * @param {string} deviceName - 设备名称
 * @returns {Promise} Promise对象
 */
function unbindUserDevice(productKey, deviceName) {
  if (!productKey) {
    return Promise.reject({ code: 1012, msg: '产品ID不能为空' });
  }
  
  if (!deviceName) {
    return Promise.reject({ code: 1012, msg: '设备名称不能为空' });
  }
  
  const requestData = {
    Action: 'UNBIND_USER_DEVICE',
    productKey: productKey,
    deviceName: deviceName
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 解绑设备需要Token
  });
}

/**
 * 查询设备状态
 * @param {string} productKey - 产品ID
 * @param {string} deviceName - 设备名称
 * @returns {Promise} Promise对象
 */
function queryDeviceStatus(productKey, deviceName) {
  // 参数验证
  const validation = validateRequiredFields({ productKey, deviceName }, ['productKey', 'deviceName']);
  if (!validation.valid) {
    return Promise.reject({ code: 1012, msg: validation.message });
  }

  const requestData = {
    Action: 'QUERY_DEVICE_STATUS',
    productKey: productKey,
    deviceName: deviceName
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 查询设备状态需要Token
  });
}

/**
 * 修改设备昵称
 * @param {string} productKey - 产品ID
 * @param {string} deviceName - 设备名称
 * @param {string} nickName - 设备昵称(最多16个字符)
 * @returns {Promise} Promise对象
 */
function updateDeviceNickName(productKey, deviceName, nickName) {
  // 参数验证
  const validation = validateRequiredFields({ productKey, deviceName, nickName }, ['productKey', 'deviceName', 'nickName']);
  if (!validation.valid) {
    return Promise.reject({ code: 1012, msg: validation.message });
  }

  if (nickName.length > 16) {
    return Promise.reject({ code: 1012, msg: '设备昵称最多16个字符' });
  }

  const requestData = {
    Action: 'UPDATE_DEVICE_NICK_NAME',
    productKey: productKey,
    deviceName: deviceName,
    nickName: nickName
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 修改设备昵称需要Token
  });
}

/**
 * 获取设备XP2P信息
 * @param {string} productKey - 产品ID
 * @param {string} deviceName - 设备名称
 * @returns {Promise} Promise对象
 */
function getDeviceXp2pInfo(productKey, deviceName) {
  // 参数验证
  const validation = validateRequiredFields({ productKey, deviceName }, ['productKey', 'deviceName']);
  if (!validation.valid) {
    return Promise.reject({ code: 1012, msg: validation.message });
  }

  const requestData = {
    Action: 'GET_DEVICE_XP2P_INFO',
    productKey: productKey,
    deviceName: deviceName
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 获取设备XP2P信息需要Token
  });
}

/**
 * 获取设备属性
 * @param {string} productKey - 产品ID
 * @param {string} deviceName - 设备名称
 * @returns {Promise} Promise对象
 */
function getDeviceThingAttribute(productKey, deviceName) {
  // 参数验证
  const validation = validateRequiredFields({ productKey, deviceName }, ['productKey', 'deviceName']);
  if (!validation.valid) {
    return Promise.reject({ code: 1012, msg: validation.message });
  }

  const requestData = {
    Action: 'GET_DEVICE_THING_ATTRIBUTE',
    productKey: productKey,
    deviceName: deviceName
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 获取设备属性需要Token
  });
}

/**
 * 查询设备告警数据
 * @param {Object} params - 查询参数
 * @param {number} params.pageNo - 当前页码，从1开始
 * @param {number} params.pageSize - 分页大小，大于等于1，小于等于100
 * @param {string} params.productKey - 产品ID
 * @param {string} params.deviceName - 设备名称
 * @param {number} params.owned - 设备拥有者 1-是 0-否
 * @param {string} params.date - 查询日期（格式：yyyy-MM-dd，可选）
 * @returns {Promise} Promise对象
 */
function queryDeviceEventData(params = {}) {
  // 参数验证
  const { pageNo = 1, pageSize = 10, productKey, deviceName, owned = 1, date } = params;

  const validation = validateRequiredFields({ productKey, deviceName }, ['productKey', 'deviceName']);
  if (!validation.valid) {
    return Promise.reject({ code: 1012, msg: validation.message });
  }

  if (!validateNumberRange(pageNo, 1, 10000)) {
    return Promise.reject({ code: 1012, msg: '页码必须为1-10000之间的正整数' });
  }

  if (!validateNumberRange(pageSize, 1, 100)) {
    return Promise.reject({ code: 1012, msg: '每页条数必须为1-100之间的正整数' });
  }

  if (owned !== 0 && owned !== 1) {
    return Promise.reject({ code: 1012, msg: '设备拥有者参数必须为0或1' });
  }

  const requestData = {
    Action: 'QUERY_DEVICE_EVENT_DATA',
    pageNo: pageNo,
    pageSize: pageSize,
    productKey: productKey,
    deviceName: deviceName,
    owned: owned
  };

  // 如果有日期参数，添加到请求中
  if (date) {
    // 简单的日期格式验证
    if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return Promise.reject({ code: 1012, msg: '日期格式必须为yyyy-MM-dd' });
    }
    requestData.date = date;
  }

  return post('/api/device/action', requestData, {
    needToken: true  // 查询设备告警数据需要Token
  });
}

/**
 * 清空设备报警消息
 * @param {string} productKey - 产品ID
 * @param {string} deviceName - 设备名称
 * @returns {Promise} Promise对象
 */
function clearDeviceEventMsg(productKey, deviceName) {
  // 参数验证
  const validation = validateRequiredFields({ productKey, deviceName }, ['productKey', 'deviceName']);
  if (!validation.valid) {
    return Promise.reject({ code: 1012, msg: validation.message });
  }

  const requestData = {
    Action: 'CLEAR_DEVICE_EVENT_MSG',
    productKey: productKey,
    deviceName: deviceName
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 清空设备报警消息需要Token
  });
}

/**
 * 用户分享设备
 * @param {string} productKey - 产品ID
 * @param {string} deviceName - 设备名称
 * @param {string} identityId - 被分享者ID
 * @param {string} permission - 分享权限信息
 * @returns {Promise} Promise对象
 */
function shareUserDevice(productKey, deviceName, identityId, permission) {
  // 参数验证
  const validation = validateRequiredFields({ productKey, deviceName, identityId, permission }, ['productKey', 'deviceName', 'identityId', 'permission']);
  if (!validation.valid) {
    return Promise.reject({ code: 1012, msg: validation.message });
  }

  const requestData = {
    Action: 'SHARE_USER_DEVICE',
    productKey: productKey,
    deviceName: deviceName,
    identityId: identityId,
    permission: permission
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 分享设备需要Token
  });
}

/**
 * 增加用户分组
 * @param {string} groupName - 分组名称(最长12个字符)
 * @param {number} groupSort - 分组排序
 * @returns {Promise} Promise对象
 */
function addUserGroup(groupName, groupSort) {
  // 参数验证
  const validation = validateRequiredFields({ groupName }, ['groupName']);
  if (!validation.valid) {
    return Promise.reject({ code: 1012, msg: validation.message });
  }

  if (groupName.length > 12) {
    return Promise.reject({ code: 1012, msg: '分组名称最长12个字符' });
  }

  if (typeof groupSort !== 'number' || groupSort < 0) {
    return Promise.reject({ code: 1012, msg: '分组排序必须为非负整数' });
  }

  const requestData = {
    Action: 'ADD_USER_GROUP',
    groupName: groupName,
    groupSort: groupSort
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 增加用户分组需要Token
  });
}

/**
 * 删除用户分组
 * @param {number} groupId - 分组ID
 * @returns {Promise} Promise对象
 */
function deleteUserGroup(groupId) {
  // 参数验证
  if (typeof groupId !== 'number' || groupId <= 0) {
    return Promise.reject({ code: 1012, msg: '分组ID必须为正整数' });
  }

  const requestData = {
    Action: 'DELETE_USER_GROUP',
    groupId: groupId
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 删除用户分组需要Token
  });
}

/**
 * 修改用户分组名称
 * @param {number} groupId - 分组ID
 * @param {string} groupName - 分组名称(最长12个字符)
 * @returns {Promise} Promise对象
 */
function updateUserGroupName(groupId, groupName) {
  // 参数验证
  const validation = validateRequiredFields({ groupName }, ['groupName']);
  if (!validation.valid) {
    return Promise.reject({ code: 1012, msg: validation.message });
  }

  if (typeof groupId !== 'number' || groupId <= 0) {
    return Promise.reject({ code: 1012, msg: '分组ID必须为正整数' });
  }

  if (groupName.length > 12) {
    return Promise.reject({ code: 1012, msg: '分组名称最长12个字符' });
  }

  const requestData = {
    Action: 'UPDATE_USER_GROUP_NAME',
    groupId: groupId,
    groupName: groupName
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 修改用户分组名称需要Token
  });
}

/**
 * 根据SN查询设备信息
 * @param {string} snCode - 设备SN码
 * @returns {Promise} Promise对象
 */
function getDeviceInfoBySN(snCode) {
  // 参数验证
  const validation = validateRequiredFields({ snCode }, ['snCode']);
  if (!validation.valid) {
    return Promise.reject({ code: 1012, msg: validation.message });
  }

  const requestData = {
    Action: 'GET_DEVICE_INFO_BY_SN',
    snCode: snCode
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 根据SN查询设备信息需要Token
  });
}

/**
 * 获取用户设备扫码TOKEN(设备扫码绑定使用)
 * @param {number} groupId - 分组ID,不传按默认分组(0)处理
 * @returns {Promise} Promise对象
 */
function getUserBindDeviceToken(groupId = 0) {
  const requestData = {
    Action: 'GET_USER_BIND_DEVICE_TOKEN'
  };

  // 如果指定了分组ID，添加到请求中
  if (groupId && groupId !== 0) {
    requestData.groupId = groupId;
  }

  return post('/api/device/action', requestData, {
    needToken: true  // 获取用户设备扫码TOKEN需要Token
  });
}

/**
 * 获取TOKEN绑定设备信息(设备扫码绑定使用)
 * @param {string} token - 用户TOKEN
 * @returns {Promise} Promise对象
 */
function getTokenBindDeviceInfo(token) {
  // 参数验证
  const validation = validateRequiredFields({ token }, ['token']);
  if (!validation.valid) {
    return Promise.reject({ code: 1012, msg: validation.message });
  }

  const requestData = {
    Action: 'GET_TOKEN_BIND_DEVICE_INFO',
    token: token
  };

  return post('/api/device/action', requestData, {
    needToken: true  // 获取TOKEN绑定设备信息需要Token
  });
}

/**
 * JCP交互
 * @param {string} productKey - 产品ID
 * @param {string} deviceName - 设备名称
 * @param {string} jcpCmd - JCP命令
 * @returns {Promise} Promise对象
 */
function jcpInteraction(productKey, deviceName, jcpCmd) {
  // 参数验证
  const validation = validateRequiredFields({ productKey, deviceName, jcpCmd }, ['productKey', 'deviceName', 'jcpCmd']);
  if (!validation.valid) {
    return Promise.reject({ code: 1012, msg: validation.message });
  }

  const requestData = {
    Action: 'DATA_SERVICE_BY_JCP',
    productKey: productKey,
    deviceName: deviceName,
    jcpCmd: jcpCmd
  };

  return post('/api/device/action', requestData, {
    needToken: true
  });
}

// 导出API函数
module.exports = {
  queryUserDeviceList,
  queryUserGroup,
  userBindDevice,
  unbindUserDevice,
  queryDeviceStatus,
  updateDeviceNickName,
  getDeviceXp2pInfo,
  getDeviceThingAttribute,
  queryDeviceEventData,
  clearDeviceEventMsg,
  shareUserDevice,
  addUserGroup,
  deleteUserGroup,
  updateUserGroupName,
  getDeviceInfoBySN,
  getUserBindDeviceToken,
  getTokenBindDeviceInfo,
  jcpInteraction
};