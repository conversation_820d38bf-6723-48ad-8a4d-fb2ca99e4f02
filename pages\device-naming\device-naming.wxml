<!--pages/device-naming/device-naming.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">设备命名</view>
  </view>

  <!-- 设备ID显示 -->
  <view class="device-id-section">
    <view class="device-id-label">设备ID:</view>
    <view class="device-id-value">{{deviceId}}</view>
  </view>

  <!-- 设备命名区域 -->
  <view class="naming-section">
    <!-- 自定义输入 -->
    <view class="input-section">
      <view class="input-label">输入设备名</view>
      <input 
        class="device-name-input" 
        placeholder="请输入设备名称"
        value="{{customName}}"
        bindinput="onCustomNameInput"
        maxlength="16"
      />
    </view>

    <!-- 快捷命名 -->
    <view class="quick-naming-section">
      <view class="quick-label">快捷命名</view>
      <view class="quick-names">
        <view 
          class="quick-name-item {{selectedQuickName === item ? 'selected' : ''}}"
          wx:for="{{quickNames}}" 
          wx:key="*this"
          data-name="{{item}}"
          bindtap="onSelectQuickName"
        >
          {{item}}
        </view>
      </view>
    </view>
  </view>

  <!-- 分组选择 -->
  <view class="group-section">
    <view class="group-header">
      <view class="group-label">选择分组</view>
      <view class="add-group-btn" bindtap="onAddGroup">添加分组</view>
    </view>
    
    <view class="group-selector">
      <picker 
        bindchange="onGroupChange" 
        value="{{selectedGroupIndex}}" 
        range="{{groups}}" 
        range-key="groupName"
        class="group-picker"
      >
        <view class="picker-display">
          <view class="group-icon">
            <text class="icon-check {{selectedGroupIndex === 0 ? 'default-group' : ''}}">✓</text>
          </view>
          <view class="group-info">
            <view class="group-name">
              {{groups[selectedGroupIndex] ? groups[selectedGroupIndex].groupName : '默认分组'}}
            </view>
            <view class="group-desc">
              ({{groups[selectedGroupIndex] ? groups[selectedGroupIndex].deviceCount : 0}}个设备)
            </view>
          </view>
        </view>
      </picker>
    </view>
  </view>

  <!-- 设备二维码 -->
  <view class="qrcode-section">
    <view class="qrcode-item" bindtap="onShowQRCode">
      <view class="qrcode-icon">
        <text class="icon-qr">📱</text>
      </view>
      <view class="qrcode-content">
        <view class="qrcode-title">设备二维码</view>
        <view class="qrcode-desc">重要信息，请妥善保管</view>
      </view>
      <view class="qrcode-action">
        <text class="action-text">保存二维码</text>
      </view>
    </view>
  </view>

  <!-- 保存按钮 -->
  <view class="save-section">
    <button 
      class="save-btn {{loading ? 'loading' : ''}}" 
      bindtap="onSaveDevice"
      disabled="{{loading}}"
    >
      {{loading ? '保存中...' : '保存'}}
    </button>
  </view>
</view>

<!-- 加载遮罩 -->
<view class="loading-mask" wx:if="{{loading}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <view class="loading-text">正在添加设备...</view>
  </view>
</view>
