const app = getApp();
const util = require('../../utils/util.js');
const { queryDeviceEventData, clearDeviceEventMsg } = require('../../api/device.js');

Page({
  data: {
    deviceId: '',
    deviceName: '',
    productKey: '',
    startDate: '',
    endDate: '',
    today: '',
    alarmGroups: [],
    loading: false,
    pageNo: 1,
    pageSize: 20,
    hasMore: true
  },

  onLoad: function(options) {
    // 从页面参数获取设备信息
    const { productKey, deviceName, nickName } = options;

    if (!productKey || !deviceName) {
      wx.showToast({
        title: '设备信息不完整',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    this.setData({
      productKey: productKey,
      deviceName: deviceName,
      deviceNickName: nickName || deviceName
    });

    // 初始化日期
    const today = new Date();
    const todayStr = util.formatDate(today);

    // 默认查询最近7天的数据
    const startDay = new Date();
    startDay.setDate(today.getDate() - 7);
    const startDayStr = util.formatDate(startDay);

    this.setData({
      today: todayStr,
      startDate: startDayStr,
      endDate: todayStr
    });

    // 加载报警数据
    this.loadAlarmMessages();
  },

  /**
   * 加载设备报警消息
   */
  loadAlarmMessages: function(isRefresh = true) {
    if (this.data.loading) {
      return;
    }

    if (isRefresh) {
      this.setData({
        pageNo: 1,
        alarmGroups: [],
        hasMore: true
      });
    }

    if (!this.data.hasMore) {
      return;
    }

    this.setData({ loading: true });

    wx.showLoading({
      title: '加载中...'
    });

    const params = {
      pageNo: this.data.pageNo,
      pageSize: this.data.pageSize,
      productKey: this.data.productKey,
      deviceName: this.data.deviceName,
      owned: 1, // 1-是设备拥有者
      date: this.data.startDate // 查询指定日期的数据
    };

    queryDeviceEventData(params)
      .then(response => {

        wx.hideLoading();
        this.setData({ loading: false });

        if (response && response.data) {
          const { data, total, pageNo } = response.data;
          const alarmList = data || [];

          // 处理报警数据，按日期分组
          const processedData = this.processAlarmData(alarmList);

          if (isRefresh) {
            this.setData({
              alarmGroups: processedData,
              pageNo: pageNo + 1,
              hasMore: alarmList.length >= this.data.pageSize
            });
          } else {
            // 合并数据
            const existingGroups = this.data.alarmGroups;
            const mergedGroups = this.mergeAlarmGroups(existingGroups, processedData);

            this.setData({
              alarmGroups: mergedGroups,
              pageNo: pageNo + 1,
              hasMore: alarmList.length >= this.data.pageSize
            });
          }

          if (alarmList.length === 0 && isRefresh) {
            wx.showToast({
              title: '暂无报警记录',
              icon: 'none'
            });
          }
        }
      })
      .catch(error => {
        wx.hideLoading();
        this.setData({ loading: false });

        // 错误已在errorHandler中处理，这里不需要额外显示
      });
  },

  /**
   * 处理报警数据，按日期分组
   */
  processAlarmData: function(alarmList) {
    const groups = {};

    alarmList.forEach(alarm => {
      // 将时间戳转换为日期
      const date = util.formatTimestamp(alarm.time, 'date');
      const time = util.formatTimestamp(alarm.time, 'datetime').split(' ')[1];

      if (!groups[date]) {
        groups[date] = {
          date: date,
          alarms: []
        };
      }

      groups[date].alarms.push({
        id: alarm.id,
        time: time,
        typeIcon: this.getAlarmTypeIcon(alarm.eventType),
        typeName: this.getAlarmTypeName(alarm.eventType),
        description: this.getAlarmDescription(alarm),
        hasImage: false, // 暂时不显示图片
        imageUrl: '',
        eventType: alarm.eventType,
        channel: alarm.channel,
        productKey: alarm.productKey,
        deviceName: alarm.deviceName
      });
    });

    // 转换为数组并按日期排序
    return Object.values(groups).sort((a, b) => {
      return util.compareDates(b.date, a.date); // 降序排列，最新的在前
    });
  },

  /**
   * 合并报警分组数据
   */
  mergeAlarmGroups: function(existingGroups, newGroups) {
    const merged = [...existingGroups];

    newGroups.forEach(newGroup => {
      const existingIndex = merged.findIndex(group => group.date === newGroup.date);
      if (existingIndex >= 0) {
        // 合并同一天的数据
        merged[existingIndex].alarms = [...merged[existingIndex].alarms, ...newGroup.alarms];
      } else {
        // 添加新的日期分组
        merged.push(newGroup);
      }
    });

    // 重新排序
    return merged.sort((a, b) => {
      return util.compareDates(b.date, a.date);
    });
  },

  /**
   * 获取报警类型图标
   */
  getAlarmTypeIcon: function(eventType) {
    const iconMap = {
      1: 'play',           // 移动侦测
      2: 'volume-o',       // 声音侦测
      3: 'friends-o',      // 人形侦测
      4: 'logistics',      // 车辆侦测
      5: 'contact',        // 人脸识别
      6: 'minus',          // 越线侦测
      7: 'location-o',     // 区域入侵
      8: 'eye-o',          // 遮挡报警
      9: 'warning-o'       // 设备离线
    };

    return iconMap[eventType] || 'warning-o';
  },

  /**
   * 获取报警类型名称
   */
  getAlarmTypeName: function(eventType) {
    const nameMap = {
      1: '移动侦测',
      2: '声音侦测',
      3: '人形侦测',
      4: '车辆侦测',
      5: '人脸识别',
      6: '越线侦测',
      7: '区域入侵',
      8: '遮挡报警',
      9: '设备离线'
    };

    return nameMap[eventType] || '未知报警';
  },

  /**
   * 获取报警描述
   */
  getAlarmDescription: function(alarm) {
    const { eventType, channel, payload } = alarm;
    let description = this.getAlarmTypeName(eventType);

    if (channel && channel > 0) {
      description += ` (通道${channel})`;
    }

    // 如果有额外信息，可以解析payload
    if (payload) {
      try {
        const payloadData = JSON.parse(payload);
        if (payloadData.description) {
          description = payloadData.description;
        }
      } catch (e) {
        // payload不是JSON格式，忽略
      }
    }

    return description;
  },

  onStartDateChange: function(e) {
    this.setData({
      startDate: e.detail.value
    });
    this.loadAlarmMessages();
  },

  onEndDateChange: function(e) {
    this.setData({
      endDate: e.detail.value
    });
    this.loadAlarmMessages();
  },

  showFilterOptions: function() {
    wx.showActionSheet({
      itemList: ['全部消息', '移动侦测', '声音侦测', '人形检测'],
      success: (res) => {
        // 根据选择的过滤条件重新获取数据
        this.loadAlarmMessages();
      }
    });
  },

  viewAlarmDetail: function(e) {
    const alarmId = e.currentTarget.dataset.id;
    // 跳转到报警详情页面，目前没有实现该页面
    wx.showToast({
      title: '报警详情功能开发中',
      icon: 'none'
    });
  },

  confirmClearAll: function() {
    wx.showModal({
      title: '清空报警消息',
      content: '确定要清空所有报警消息吗？此操作无法撤销。',
      confirmColor: '#f5222d',
      success: (res) => {
        if (res.confirm) {
          this.clearAllAlarms();
        }
      }
    });
  },

  confirmClearAll: function() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有报警消息吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.clearAllAlarms();
        }
      }
    });
  },

  clearAllAlarms: function() {
    wx.showLoading({
      title: '清空中...'
    });

    console.log('=== 清空设备报警消息 ===');
    console.log('产品ID:', this.data.productKey);
    console.log('设备名称:', this.data.deviceName);

    clearDeviceEventMsg(this.data.productKey, this.data.deviceName)
      .then(response => {
        console.log('=== 清空设备报警消息成功 ===');
        console.log('响应数据:', response);

        wx.hideLoading();

        // 清空本地数据
        this.setData({
          alarmGroups: []
        });

        wx.showToast({
          title: '报警消息已清空',
          icon: 'success'
        });
      })
      .catch(error => {
        console.error('=== 清空设备报警消息失败 ===');
        console.error('错误信息:', error);

        wx.hideLoading();

        // 错误已在errorHandler中处理，这里不需要额外显示
      });
  },

  onPullDownRefresh: function() {
    this.loadAlarmMessages();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  onReachBottom: function() {
    // 加载更多数据
    this.loadAlarmMessages(false);
  }
}); 