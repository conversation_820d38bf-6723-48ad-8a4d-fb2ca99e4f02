const app = getApp();
const deviceApi = require('../../api/device');
const { isLoggedIn, handleApiError } = require('../../utils/auth');
const { extractDeviceSnFromUrl } = require('../../utils/urlUtils');

Page({
  data: {
    deviceSerialNumber: '', // 手动输入的设备序列号
    loading: false,
    groups: [], // 分组列表
    selectedGroupId: 0, // 选中的分组ID，0表示默认分组
    addMethods: [
      {
        id: 'qrcode',
        title: '扫码添加',
        desc: '扫描设备二维码',
        icon: 'scan'
      },
      {
        id: 'manual',
        title: '序列号',
        desc: '输入设备SN码',
        icon: 'edit'
      },
      {
        id: 'bluetooth',
        title: '蓝牙连接',
        desc: '搜索蓝牙设备',
        icon: 'bluetooth'
      }
    ]
  },

  onLoad: function(options) {
    // 检查是否已登录
    if (!isLoggedIn()) {
      wx.showModal({
        title: '请先登录',
        content: '添加设备需要先登录账号',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/profile/profile'
          });
        }
      });
      return;
    }

    // 加载分组列表
    this.loadGroupList();
  },

  /**
   * 返回按钮点击事件
   */
  onBackClick: function() {
    wx.navigateBack();
  },

  /**
   * 加载分组列表
   */
  loadGroupList: function() {
    deviceApi.queryUserGroup()
      .then(response => {
        if (response && response.data) {
          // 添加默认分组选项
          const groups = [
            { groupId: 0, groupName: '默认分组', groupSort: 0 },
            ...response.data
          ];
          
          this.setData({
            groups: groups
          });
        }
      })
      .catch(error => {
        console.error('获取分组列表失败:', error);
      });
  },

  /**
   * 选择添加方式
   */
  onSelectAddMethod: function(e) {
    const method = e.currentTarget.dataset.method;
    
    switch (method) {
      case 'qrcode':
        this.onScanQRCode();
        break;
      case 'manual':
        this.showManualAddDialog();
        break;
      case 'bluetooth':
        this.onBluetoothConnect();
        break;
    }
  },

  /**
   * 扫描二维码
   */
  onScanQRCode: function() {
    wx.scanCode({
      scanType: ['qrCode'],
      success: (res) => {
        this.processQRCodeResult(res.result);
      },
      fail: (err) => {
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 处理二维码扫描结果
   */
  processQRCodeResult: function(result) {
    console.log('=== 处理二维码扫描结果 ===');
    console.log('扫描结果:', result);

    wx.showLoading({
      title: '正在处理...'
    });

    // 首先检查是否是URL格式的二维码
    if (this.isUrlQRCode(result)) {
      this.processUrlQRCode(result);
      return;
    }

    // 检查是否是TOKEN格式的二维码
    if (this.isTokenQRCode(result)) {
      this.processTokenQRCode(result);
      return;
    }

    try {
      // 尝试解析JSON格式的二维码
      const deviceInfo = JSON.parse(result);

      // 验证必要字段
      if (!deviceInfo.productKey || !deviceInfo.deviceName) {
        throw new Error('二维码格式不正确');
      }

      console.log('JSON格式二维码，设备信息:', deviceInfo);
      wx.hideLoading();
      this.navigateToDeviceNaming(deviceInfo);

    } catch (e) {
      // 如果不是JSON格式，当作序列号处理
      const serialNumber = result.trim();

      // 验证序列号格式
      if (serialNumber.length < 8 || !/^[A-Za-z0-9]+$/.test(serialNumber)) {
        wx.hideLoading();
        wx.showModal({
          title: '二维码格式错误',
          content: '扫描到的二维码不是有效的设备信息',
          showCancel: false
        });
        return;
      }

      console.log('序列号格式二维码，序列号:', serialNumber);
      this.getDeviceInfoAndNavigate(serialNumber);
    }
  },

  /**
   * 判断是否是URL格式的二维码
   */
  isUrlQRCode: function(result) {
    // 检查是否是URL格式，包含sncode参数
    return result && (
      result.startsWith('http://') ||
      result.startsWith('https://')
    ) && result.includes('sncode=');
  },

  /**
   * 处理URL格式的二维码
   */
  processUrlQRCode: function(url) {
    console.log('=== 处理URL格式二维码 ===');
    console.log('URL:', url);

    // 使用工具函数提取设备序列号
    const sncode = extractDeviceSnFromUrl(url);

    if (!sncode) {
      wx.hideLoading();
      wx.showModal({
        title: '二维码格式错误',
        content: '无法从二维码URL中提取有效的设备序列号',
        showCancel: false
      });
      return;
    }

    // 使用提取的序列号查询设备信息
    this.getDeviceInfoAndNavigate(sncode);
  },

  /**
   * 判断是否是TOKEN格式的二维码
   */
  isTokenQRCode: function(result) {
    return result && result.length === 32 && /^[a-f0-9]+$/i.test(result);
  },

  /**
   * 处理TOKEN格式的二维码
   */
  processTokenQRCode: function(token) {
    console.log('=== 处理TOKEN格式二维码 ===');
    console.log('TOKEN:', token);

    deviceApi.getTokenBindDeviceInfo(token)
      .then(response => {
        console.log('=== TOKEN查询成功 ===');
        console.log('响应数据:', response);

        if (response && response.data) {
          const { productKey, deviceName, snCode, status } = response.data;

          if (status === 0) {
            // 设备未绑定，跳转到命名页面
            const deviceInfo = {
              productKey: productKey,
              deviceName: deviceName,
              snCode: snCode
            };
            wx.hideLoading();
            this.navigateToDeviceNaming(deviceInfo);
          } else {
            wx.hideLoading();
            this.handleBindStatus(status);
          }
        } else {
          wx.hideLoading();
          wx.showModal({
            title: '获取设备信息失败',
            content: '无法获取设备信息，请重试',
            showCancel: false
          });
        }
      })
      .catch(error => {
        wx.hideLoading();
        console.error('=== TOKEN查询失败 ===');
        console.error('错误信息:', error);
      });
  },

  /**
   * 处理绑定状态
   */
  handleBindStatus: function(status) {
    const statusMessages = {
      1: '设备绑定成功',
      2: '设备已被您添加',
      3: '该设备已被其他用户绑定'
    };

    const message = statusMessages[status] || '未知状态';
    
    if (status === 1) {
      wx.showToast({
        title: message,
        icon: 'success'
      });
      this.navigateBackToDeviceList();
    } else {
      wx.showModal({
        title: '设备状态',
        content: message,
        showCancel: false
      });
    }
  },

  /**
   * 显示手动添加对话框
   */
  showManualAddDialog: function() {
    wx.showModal({
      title: '手动添加设备',
      content: '请输入设备序列号',
      editable: true,
      placeholderText: '请输入8位以上的序列号',
      success: (res) => {
        if (res.confirm && res.content) {
          const serialNumber = res.content.trim();
          this.validateAndAddDevice(serialNumber);
        }
      }
    });
  },

  /**
   * 验证并添加设备
   */
  validateAndAddDevice: function(serialNumber) {
    // 验证序列号格式
    if (serialNumber.length < 8) {
      wx.showToast({
        title: '序列号长度不能少于8位',
        icon: 'none'
      });
      return;
    }

    if (!/^[A-Za-z0-9]+$/.test(serialNumber)) {
      wx.showToast({
        title: '序列号只能包含字母和数字',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '正在验证设备...'
    });

    this.getDeviceInfoAndNavigate(serialNumber);
  },

  /**
   * 根据序列号获取设备信息并跳转到命名页面
   */
  getDeviceInfoAndNavigate: function(serialNumber) {
    deviceApi.getDeviceInfoBySN(serialNumber)
      .then(response => {
        if (response && response.data) {
          const deviceInfo = {
            productKey: response.data.productKey,
            deviceName: response.data.deviceName,
            mac: response.data.mac,
            snCode: serialNumber
          };
          wx.hideLoading();
          this.navigateToDeviceNaming(deviceInfo);
        } else {
          wx.hideLoading();
          wx.showModal({
            title: '设备不存在',
            content: '未找到该序列号对应的设备，请检查序列号是否正确',
            showCancel: false
          });
        }
      })
      .catch(error => {
        wx.hideLoading();
        console.error('查询设备信息失败:', error);
      });
  },

  /**
   * 跳转到设备命名页面
   */
  navigateToDeviceNaming: function(deviceInfo) {
    const { productKey, deviceName, snCode, mac } = deviceInfo;

    wx.navigateTo({
      url: `/pages/device-naming/device-naming?productKey=${productKey}&deviceName=${deviceName}&snCode=${snCode}&mac=${mac || ''}`
    });
  },

  /**
   * 绑定设备
   */
  bindDeviceWithInfo: function(deviceInfo) {
    const { productKey, deviceName, snCode } = deviceInfo;
    const nickName = deviceName || snCode;
    const groupId = this.data.selectedGroupId;

    console.log('=== 开始绑定设备 ===');
    console.log('设备信息:', deviceInfo);
    console.log('分组ID:', groupId);

    deviceApi.userBindDevice(productKey, deviceName, nickName, groupId)
      .then(response => {
        console.log('=== 设备绑定成功 ===');
        console.log('响应数据:', response);

        wx.hideLoading();

        if (response && response.data) {
          const bindResult = response.data.bind;
          
          if (bindResult === 200) {
            wx.showToast({
              title: '设备添加成功',
              icon: 'success'
            });
            this.navigateBackToDeviceList();
          } else if (bindResult === 1036) {
            wx.showToast({
              title: '设备已被您添加',
              icon: 'none'
            });
          } else if (bindResult === 1037) {
            const userName = response.data.userName || '其他用户';
            wx.showModal({
              title: '设备已被绑定',
              content: `该设备已被用户"${userName}"绑定`,
              showCancel: false
            });
          }
        } else {
          wx.showToast({
            title: '设备添加成功',
            icon: 'success'
          });
          this.navigateBackToDeviceList();
        }
      })
      .catch(error => {
        console.error('=== 设备绑定失败 ===');
        console.error('错误信息:', error);
        wx.hideLoading();
      });
  },

  /**
   * 蓝牙连接（暂时显示开发中提示）
   */
  onBluetoothConnect: function() {
    wx.showToast({
      title: '蓝牙功能开发中',
      icon: 'none'
    });
  },

  /**
   * 分组选择变化
   */
  onGroupChange: function(e) {
    const groupId = parseInt(e.detail.value);
    this.setData({
      selectedGroupId: groupId
    });
  },

  /**
   * 返回设备列表页面
   */
  navigateBackToDeviceList: function() {
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  }
});
