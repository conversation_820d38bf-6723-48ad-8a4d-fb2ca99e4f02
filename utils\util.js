/**
 * 通用工具函数
 */

/**
 * 格式化日期为 YYYY-MM-DD 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
  if (!date || !(date instanceof Date)) {
    return '';
  }
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
}

/**
 * 格式化时间为 YYYY-MM-DD HH:mm:ss 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的时间字符串
 */
function formatDateTime(date) {
  if (!date || !(date instanceof Date)) {
    return '';
  }
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 格式化时间戳为日期字符串
 * @param {number} timestamp - 时间戳（毫秒）
 * @param {string} format - 格式类型 'date' | 'datetime'
 * @returns {string} 格式化后的字符串
 */
function formatTimestamp(timestamp, format = 'datetime') {
  if (!timestamp || typeof timestamp !== 'number') {
    return '';
  }
  
  const date = new Date(timestamp);
  
  if (format === 'date') {
    return formatDate(date);
  } else {
    return formatDateTime(date);
  }
}

/**
 * 获取今天的日期字符串
 * @returns {string} YYYY-MM-DD 格式的今天日期
 */
function getToday() {
  return formatDate(new Date());
}

/**
 * 获取指定天数前的日期字符串
 * @param {number} days - 天数
 * @returns {string} YYYY-MM-DD 格式的日期
 */
function getDaysAgo(days) {
  const date = new Date();
  date.setDate(date.getDate() - days);
  return formatDate(date);
}

/**
 * 解析日期字符串为Date对象
 * @param {string} dateStr - 日期字符串 YYYY-MM-DD
 * @returns {Date|null} Date对象或null
 */
function parseDate(dateStr) {
  if (!dateStr || typeof dateStr !== 'string') {
    return null;
  }
  
  const date = new Date(dateStr);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * 比较两个日期字符串
 * @param {string} date1 - 日期字符串1
 * @param {string} date2 - 日期字符串2
 * @returns {number} -1: date1 < date2, 0: date1 = date2, 1: date1 > date2
 */
function compareDates(date1, date2) {
  const d1 = parseDate(date1);
  const d2 = parseDate(date2);
  
  if (!d1 || !d2) {
    return 0;
  }
  
  if (d1.getTime() < d2.getTime()) {
    return -1;
  } else if (d1.getTime() > d2.getTime()) {
    return 1;
  } else {
    return 0;
  }
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
function debounce(func, delay) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, delay) {
  let lastCall = 0;
  return function (...args) {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      return func.apply(this, args);
    }
  };
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item));
  }
  
  if (typeof obj === 'object') {
    const cloned = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度
 * @returns {string} 随机字符串
 */
function generateRandomString(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

module.exports = {
  formatDate,
  formatDateTime,
  formatTimestamp,
  getToday,
  getDaysAgo,
  parseDate,
  compareDates,
  debounce,
  throttle,
  deepClone,
  generateRandomString
};
