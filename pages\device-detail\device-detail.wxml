<view class="page">
  <!-- 顶部导航栏 -->
  <van-nav-bar
    title="设备详情"
    left-arrow
    fixed
    placeholder
    bind:click-left="onBackClick"
  />

  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" size="24px" vertical>
    加载设备信息中...
  </van-loading>

  <!-- 设备详情内容 -->
  <view class="container" wx:else>
    <!-- 视频播放区域 -->
    <view class="video-container" wx:if="{{device.status === 1}}">
      <!-- 视频播放器 -->
      <live-player
        wx:if="{{isPlaying && videoUrl}}"
        id="videoPlayer"
        src="{{videoUrl}}"
        mode="live"
        autoplay="{{true}}"
        muted="{{isMuted}}"
        object-fit="contain"
        class="live-player"
        bindstatechange="onPlayerStateChange"
        binderror="onPlayerError"
      />

      <!-- 视频占位图 -->
      <view class="video-placeholder" wx:else bindtap="startPlay">
        <view class="placeholder-content">
          <van-icon name="play-circle-o" size="48px" color="#1989fa" />
          <view class="placeholder-text">点击开始播放</view>
        </view>
      </view>

      <!-- 视频控制按钮 -->
      <view class="video-controls">
        <view class="control-btn" bindtap="togglePlay">
          <van-icon name="{{isPlaying ? 'pause-circle-o' : 'play-circle-o'}}" size="24px" color="#fff" />
        </view>
        <view class="control-btn" bindtap="toggleMute">
          <van-icon name="{{isMuted ? 'volume-o' : 'volume'}}" size="24px" color="#fff" />
        </view>
      </view>
    </view>

    <!-- 设备离线提示 -->
    <view class="video-container offline" wx:else>
      <view class="placeholder-content">
        <van-icon name="warning-o" size="48px" color="#ff4d4f" />
        <view class="placeholder-text">设备离线，无法播放</view>
      </view>
    </view>

    <!-- 设备基本信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <view class="device-info">
          <view class="device-name" bindtap="showEditName">
            {{device.nickName}}
            <van-icon name="edit" size="16px" color="#969799" />
          </view>
          <view class="device-id">{{device.deviceName}}</view>
        </view>
        <view class="device-status {{getStatusClass(device.status)}}">
          {{getStatusText(device.status)}}
        </view>
      </view>

      <view class="card-content">
        <view class="info-row">
          <view class="info-item">
            <view class="info-label">产品Key</view>
            <view class="info-value">{{device.productKey}}</view>
          </view>
        </view>

        <view class="info-row" wx:if="{{device.xp2pInfo}}">
          <view class="info-item">
            <view class="info-label">XP2P信息</view>
            <view class="info-value">{{device.xp2pInfo}}</view>
          </view>
        </view>

        <view class="info-row" wx:if="{{device.cloudStorageEnabled}}">
          <view class="info-item">
            <view class="info-label">云存储</view>
            <view class="info-value">已开启 ({{device.cloudStorageDays}}天)</view>
          </view>
        </view>

        <view class="info-row" wx:if="{{device.supportPtz}}">
          <view class="info-item">
            <view class="info-label">云台控制</view>
            <view class="info-value">支持</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="function-menu">
      <view class="menu-title">设备功能</view>
      <view class="menu-items">
        <view class="menu-item" bindtap="goToAlarm">
          <van-icon name="warning-o" size="20px" color="#ff4d4f" />
          <text>报警消息</text>
          <van-icon name="arrow" size="16px" color="#c8c9cc" />
        </view>

        <view class="menu-item" bindtap="shareDevice">
          <van-icon name="friends-o" size="20px" color="#1989fa" />
          <text>设备分享</text>
          <van-icon name="arrow" size="16px" color="#c8c9cc" />
        </view>
      </view>
    </view>

    <!-- 设备属性信息 -->
    <view class="attributes-card" wx:if="{{device.attributes}}">
      <view class="card-title">设备属性</view>
      <view class="attributes-content">
        <block wx:for="{{device.attributes}}" wx:key="index" wx:for-item="attr" wx:for-index="key">
          <view class="attr-item" wx:if="{{attr.Value !== undefined}}">
            <view class="attr-key">{{key}}</view>
            <view class="attr-value">{{attr.Value}}</view>
          </view>
        </block>
      </view>
    </view>

    <!-- 底部操作区 -->
    <view class="bottom-actions">
      <van-button type="danger" block bindtap="confirmUnbindDevice">
        解绑设备
      </van-button>
    </view>
  </view>

  <!-- 下拉刷新提示 -->
  <van-toast id="van-toast" />
</view>