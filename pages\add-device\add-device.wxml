<!--pages/add-device/add-device.wxml-->
<!-- 顶部导航栏 -->
<van-nav-bar
  title="添加设备"
  left-text="返回"
  left-arrow
  fixed
  placeholder
  bind:click-left="onBackClick"
  custom-class="custom-nav"
  title-class="nav-title"
/>

<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-desc">选择添加方式连接您的智能设备</view>
  </view>

  <!-- 添加方式选择 -->
  <view class="add-methods-container">
    <view class="method-card" 
      wx:for="{{addMethods}}" 
      wx:key="id"
      data-method="{{item.id}}"
      bindtap="onSelectAddMethod">
      <view class="method-icon">
        <view class="icon-bg">
          <text class="icon icon-{{item.icon}}"></text>
        </view>
      </view>
      <view class="method-name">{{item.title}}</view>
      <view class="method-desc">{{item.desc}}</view>
    </view>
  </view>

  <!-- 分组选择 -->
  <view class="group-selector">
    <view class="selector-label">设备分组</view>
    <picker 
      bindchange="onGroupChange" 
      value="{{selectedGroupId}}" 
      range="{{groups}}" 
      range-key="groupName"
      class="group-picker">
      <view class="picker-display">
        <text class="picker-text">
          {{groups[selectedGroupId] ? groups[selectedGroupId].groupName : '默认分组'}}
        </text>
        <text class="picker-arrow">></text>
      </view>
    </picker>
  </view>

  <!-- 帮助说明 -->
  <view class="help-section">
    <view class="help-title">使用说明</view>
    <view class="help-content">
      <view class="help-item">
        <text class="help-dot"></text>
        <text class="help-text">扫描二维码：扫描设备上或包装盒上的二维码</text>
      </view>
      <view class="help-item">
        <text class="help-dot"></text>
        <text class="help-text">序列号添加：手动输入设备标签上的SN码</text>
      </view>
      <view class="help-item">
        <text class="help-dot"></text>
        <text class="help-text">蓝牙连接：打开蓝牙搜索周围可连接的设备</text>
      </view>
    </view>
  </view>
</view>

<!-- 加载遮罩 -->
<view class="loading-mask" wx:if="{{loading}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <view class="loading-text">正在处理...</view>
  </view>
</view>
