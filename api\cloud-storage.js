const { post } = require('../utils/request');
const { validateNumberRange } = require('../utils/validator');

/**
 * 查询云存储套餐列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNo - 当前页码，从1开始
 * @param {number} params.pageSize - 分页大小，大于等于1，小于等于100
 * @returns {Promise} Promise对象
 */
function queryCloudStorageCommoditys(params = {}) {
  // 参数验证
  const pageNo = params.pageNo || 1;
  const pageSize = params.pageSize || 10;
  
  if (!validateNumberRange(pageNo, 1, 10000)) {
    return Promise.reject({ code: 1012, msg: '页码必须为1-10000之间的正整数' });
  }

  if (!validateNumberRange(pageSize, 1, 100)) {
    return Promise.reject({ code: 1012, msg: '每页条数必须为1-100之间的正整数' });
  }

  const requestData = {
    Action: 'QUERY_CLOUD_STORAGE_COMMODITYS',
    pageNo: pageNo,
    pageSize: pageSize
  };

  // console.log('=== 查询云存储套餐列表API请求 ===');
  // console.log('请求URL: /api/device/action');
  // console.log('请求参数:', JSON.stringify(requestData, null, 2));
  // console.log('=== 查询云存储套餐列表API请求结束 ===');

  return post('/api/device/action', requestData, {
    needToken: true  // 查询云存储套餐列表需要Token
  });
}

/**
 * 云存储下单
 * @param {Object} params - 订单参数
 * @param {string} params.productKey - 产品ID
 * @param {string} params.deviceName - 设备名称
 * @param {string} params.commodityId - 商品ID
 * @param {number} params.quantity - 购买数量
 * @param {number} params.autoRenew - 是否自动续费 0-否 1-是
 * @returns {Promise} Promise对象
 */
function cloudStorageOrder(params) {
  // 参数验证
  if (!params || typeof params !== 'object') {
    return Promise.reject({ code: 1012, msg: '参数错误' });
  }

  if (!params.productKey) {
    return Promise.reject({ code: 1012, msg: '产品ID不能为空' });
  }

  if (!params.deviceName) {
    return Promise.reject({ code: 1012, msg: '设备名称不能为空' });
  }

  if (!params.commodityId) {
    return Promise.reject({ code: 1012, msg: '商品ID不能为空' });
  }

  const quantity = params.quantity || 1;
  if (!validateNumberRange(quantity, 1, 100)) {
    return Promise.reject({ code: 1012, msg: '购买数量必须为1-100之间的正整数' });
  }

  const autoRenew = params.autoRenew === undefined ? 0 : params.autoRenew;
  if (![0, 1].includes(autoRenew)) {
    return Promise.reject({ code: 1012, msg: '自动续费参数必须为0或1' });
  }

  const requestData = {
    Action: 'CLOUD_STORAGE_ORDER',
    productKey: params.productKey,
    deviceName: params.deviceName,
    commodityId: params.commodityId,
    quantity: quantity,
    autoRenew: autoRenew
  };

  // console.log('=== 云存储下单API请求 ===');
  // console.log('请求URL: /api/device/action');
  // console.log('请求参数:', JSON.stringify(requestData, null, 2));
  // console.log('=== 云存储下单API请求结束 ===');

  return post('/api/device/action', requestData, {
    needToken: true  // 云存储下单需要Token
  });
}

/**
 * 查询云存储信息
 * @param {Object} params - 查询参数
 * @param {string} params.productKey - 产品ID
 * @param {string} params.deviceName - 设备名称
 * @returns {Promise} Promise对象
 */
function queryCloudStorage(params) {
  // 参数验证
  if (!params || typeof params !== 'object') {
    return Promise.reject({ code: 1012, msg: '参数错误' });
  }

  if (!params.productKey) {
    return Promise.reject({ code: 1012, msg: '产品ID不能为空' });
  }

  if (!params.deviceName) {
    return Promise.reject({ code: 1012, msg: '设备名称不能为空' });
  }

  const requestData = {
    Action: 'QUERY_CLOUD_STORAGE',
    productKey: params.productKey,
    deviceName: params.deviceName
  };

  // console.log('=== 查询云存储信息API请求 ===');
  // console.log('请求URL: /api/device/action');
  // console.log('请求参数:', JSON.stringify(requestData, null, 2));
  // console.log('=== 查询云存储信息API请求结束 ===');

  return post('/api/device/action', requestData, {
    needToken: true  // 查询云存储信息需要Token
  });
}

/**
 * 查询云存储自动续费状态
 * @param {Object} params - 查询参数
 * @param {string} params.productKey - 产品ID
 * @param {string} params.deviceName - 设备名称
 * @returns {Promise} Promise对象
 */
function getCloudStorageAutoRenew(params) {
  // 参数验证
  if (!params || typeof params !== 'object') {
    return Promise.reject({ code: 1012, msg: '参数错误' });
  }

  if (!params.productKey) {
    return Promise.reject({ code: 1012, msg: '产品ID不能为空' });
  }

  if (!params.deviceName) {
    return Promise.reject({ code: 1012, msg: '设备名称不能为空' });
  }

  const requestData = {
    Action: 'GET_CLOUD_STORAGE_AUTO_RENEW',
    productKey: params.productKey,
    deviceName: params.deviceName
  };

  // console.log('=== 查询云存储自动续费状态API请求 ===');
  // console.log('请求URL: /api/device/action');
  // console.log('请求参数:', JSON.stringify(requestData, null, 2));
  // console.log('=== 查询云存储自动续费状态API请求结束 ===');

  return post('/api/device/action', requestData, {
    needToken: true  // 查询云存储自动续费状态需要Token
  });
}

/**
 * 设置云存储自动续费
 * @param {Object} params - 设置参数
 * @param {string} params.productKey - 产品ID
 * @param {string} params.deviceName - 设备名称
 * @param {number} params.autoRenew - 是否自动续费 0-否 1-是
 * @returns {Promise} Promise对象
 */
function setCloudStorageAutoRenew(params) {
  // 参数验证
  if (!params || typeof params !== 'object') {
    return Promise.reject({ code: 1012, msg: '参数错误' });
  }

  if (!params.productKey) {
    return Promise.reject({ code: 1012, msg: '产品ID不能为空' });
  }

  if (!params.deviceName) {
    return Promise.reject({ code: 1012, msg: '设备名称不能为空' });
  }

  if (![0, 1].includes(params.autoRenew)) {
    return Promise.reject({ code: 1012, msg: '自动续费参数必须为0或1' });
  }

  const requestData = {
    Action: 'SET_CLOUD_STORAGE_AUTO_RENEW',
    productKey: params.productKey,
    deviceName: params.deviceName,
    autoRenew: params.autoRenew
  };

  // console.log('=== 设置云存储自动续费API请求 ===');
  // console.log('请求URL: /api/device/action');
  // console.log('请求参数:', JSON.stringify(requestData, null, 2));
  // console.log('=== 设置云存储自动续费API请求结束 ===');

  return post('/api/device/action', requestData, {
    needToken: true  // 设置云存储自动续费需要Token
  });
}

// 导出API函数
module.exports = {
  queryCloudStorageCommoditys,
  cloudStorageOrder,
  queryCloudStorage,
  getCloudStorageAutoRenew,
  setCloudStorageAutoRenew
}; 