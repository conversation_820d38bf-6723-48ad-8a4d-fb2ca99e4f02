.container {
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.device-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.device-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.device-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.share-methods {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.method-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 30rpx;
  font-weight: bold;
}

.method-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.method-item:last-child {
  border-bottom: none;
}

.method-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 20rpx;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.method-desc {
  font-size: 24rpx;
  color: #999;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}

.shared-users {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 30rpx;
  font-weight: bold;
}

.user-count {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
  margin-left: 10rpx;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-item:last-child {
  border-bottom: none;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.share-time {
  font-size: 24rpx;
  color: #999;
}

.delete-btn {
  padding: 10rpx;
}

.delete-icon {
  width: 32rpx;
  height: 32rpx;
}

.no-user {
  padding: 30rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #999;
}
