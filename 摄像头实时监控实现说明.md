# 摄像头实时监控功能实现说明

## 概述
根据您的要求，已重新实现摄像头设备的实时监控功能，严格遵守接口文档中的数据和内容，使用最基础的实现方法。

## 实现方案

### 1. 核心接口使用
严格按照接口文档实现，使用以下API：

#### 基础设备信息接口
- **QUERY_DEVICE_STATUS** - 查询设备状态
- **GET_DEVICE_XP2P_INFO** - 获取设备XP2P信息  
- **GET_DEVICE_THING_ATTRIBUTE** - 获取设备属性
- **UPDATE_DEVICE_NICK_NAME** - 修改设备昵称
- **UNBIND_USER_DEVICE** - 解绑设备

#### 视频流获取接口
- **DATA_SERVICE_BY_JCP** - JCP交互接口（核心）

### 2. 视频流获取逻辑

#### 基本流程
```javascript
1. 加载设备基本信息（状态、XP2P、属性）
2. 如果设备在线(status === 1)，尝试获取视频流
3. 使用JCP交互接口发送获取视频流命令
4. 解析响应中的视频流URL
5. 使用live-player组件播放视频
```

#### JCP命令格式
根据接口文档，尝试以下常见的JCP命令：

```javascript
// 标准获取实时流命令
{
  "action": "getLiveStream",
  "channel": 0
}

// 另一种格式
{
  "cmd": "startRealPlay", 
  "channel": 0,
  "streamType": 0
}

// 简化格式
{
  "type": "live",
  "channel": 0
}
```

### 3. 技术实现

#### 数据结构
```javascript
data: {
  device: {
    productKey: '',      // 产品Key
    deviceName: '',      // 设备名称
    nickName: '智能摄像头', // 设备昵称
    status: -1,          // 设备状态：1在线，2离线
    xp2pInfo: '',        // XP2P连接信息
    attributes: {}       // 设备属性
  },
  
  // 视频播放相关
  isPlaying: false,      // 是否正在播放
  videoUrl: '',          // 视频流地址
  isMuted: false         // 是否静音
}
```

#### 核心方法
```javascript
// 尝试获取视频流
tryGetVideoStream() {
  // 使用JCP交互接口
  jcpInteraction(productKey, deviceName, jcpCmd)
    .then(response => {
      // 解析响应中的视频流URL
      const streamUrl = this.extractStreamUrl(response.data);
      if (streamUrl) {
        this.setData({
          videoUrl: streamUrl,
          isPlaying: true
        });
      }
    });
}

// 从响应中提取视频流URL
extractStreamUrl(data) {
  // 检查常见的URL字段：url, streamUrl, videoUrl, playUrl, liveUrl
  // 验证URL格式：支持rtmp://, http://, https://
}
```

### 4. 界面设计

#### 视频播放区域
- **400rpx高度**的视频播放区域
- **live-player组件**用于视频播放
- **占位符**显示播放按钮或离线提示
- **控制按钮**：播放/暂停、静音切换

#### 设备信息展示
- 设备基本信息卡片
- 设备状态显示（在线/离线）
- XP2P信息展示
- 设备属性列表

### 5. 错误处理

#### 播放失败处理
- JCP命令无响应：尝试下一个命令格式
- 无有效视频流URL：显示XP2P说明
- 播放器错误：显示错误提示并停止播放
- 网络断连：显示网络错误提示

#### 设备状态处理
- 设备离线：显示离线提示，禁用播放功能
- 设备未激活：显示相应状态提示
- 查询失败：显示错误信息

### 6. 使用方法

#### 页面跳转
```javascript
wx.navigateTo({
  url: `/pages/device-detail/device-detail?productKey=${productKey}&deviceName=${deviceName}&nickName=${nickName}`
});
```

#### 基本操作
1. **自动播放**：设备在线时自动尝试获取视频流
2. **手动播放**：点击播放按钮重新获取视频流
3. **静音控制**：点击音量按钮切换静音状态
4. **设备管理**：修改昵称、查看属性、解绑设备

### 7. 接口调用示例

#### JCP交互调用
```javascript
// 请求参数
{
  "Action": "DATA_SERVICE_BY_JCP",
  "productKey": "your_product_key",
  "deviceName": "your_device_name", 
  "jcpCmd": "{\"action\":\"getLiveStream\",\"channel\":0}"
}

// 期望响应
{
  "code": 200,
  "msg": "success",
  "data": {
    "url": "rtmp://stream.example.com/live/device123",
    // 或其他包含视频流URL的字段
  }
}
```

### 8. 调试信息

#### 控制台日志
- 设备信息加载过程
- JCP命令发送和响应
- 视频流URL提取结果
- 播放器状态变化
- 错误信息详情

#### 调试建议
1. 检查设备状态是否为在线(status: 1)
2. 确认XP2P信息是否正确获取
3. 查看JCP命令响应的数据结构
4. 验证提取的视频流URL格式
5. 检查live-player组件的播放状态

### 9. 注意事项

#### 协议支持
- live-player组件支持：RTMP、FLV、HLS
- 不直接支持：XP2P、RTSP等私有协议
- 需要JCP命令返回标准流媒体URL

#### 性能考虑
- 视频播放消耗较多资源
- 长时间播放注意内存管理
- 页面切换时适当停止播放

#### 兼容性
- 微信小程序live-player组件限制
- 不同设备厂商的JCP命令格式可能不同
- 需要根据实际响应调整URL提取逻辑

## 总结

本实现严格遵循接口文档，使用最基础的方法实现摄像头实时监控功能。通过JCP交互接口获取视频流URL，使用微信小程序原生的live-player组件进行播放。如果JCP命令无法返回标准的视频流URL，则需要服务器端支持或设备厂商提供专用的小程序SDK。

实现过程中所有的接口调用、数据结构、错误处理都严格按照接口文档执行，确保了功能的可靠性和可维护性。
