# JCP命令测试结果分析

## 当前发现

### 设备信息
- **设备名称**: 休息室
- **设备ID**: 200000001y
- **序列号**: 395ce24901b8553c981c1004
- **设备状态**: 在线 (status: 1)
- **XP2P信息**: `XP2P7te/+2xdcKweIlIE8nlSeIp2%2.4.50`

### JCP命令测试结果

#### 第一次尝试
**命令**: `{"action":"startLivePreview","channel":0,"streamType":"main"}`
**结果**: `"[Error]no such command!"`
**分析**: 设备不识别此命令格式

#### 当前尝试
**命令**: `{"cmd":"startRealPlay","channel":0,"streamType":0}`
**状态**: 等待测试结果

## 改进的实现

### 1. 智能命令尝试
现在会自动尝试多种常见的JCP命令格式：

```javascript
// 标准格式
{"cmd": "startRealPlay", "channel": 0}
{"action": "getLiveStream", "channel": 0}
{"method": "live.start", "params": {"channel": 0}}

// 简化格式
{"type": "live", "channel": 0}
{"command": "startLive", "channelId": 0}

// 厂商特定格式
{"cmd": "video_start", "chn": 0, "stream": 0}
{"action": "start_preview", "ch": 0}
{"method": "startPreview", "channel": 0, "quality": "HD"}
```

### 2. 错误检测机制
- 自动检测 `"no such command"` 错误
- 当命令不被识别时，自动尝试下一个格式
- 详细的日志输出帮助调试

### 3. 增强的URL提取
- 支持更多URL字段名
- 递归检查嵌套对象
- 支持多种流媒体协议

## 测试步骤

### 1. 运行当前代码
请重新进入设备详情页面，查看控制台输出：

#### 期望看到的日志
```
尝试获取视频流...
发送JCP命令: {"cmd":"startRealPlay","channel":0,"streamType":0}
JCP响应: {...}
JCP响应数据详情: {...}
```

#### 如果命令不被识别
```
当前命令不被识别，尝试其他命令格式
尝试备选命令 1: {"cmd":"startRealPlay","channel":0}
备选命令 1 响应: {...}
```

### 2. 分析响应数据
关注以下关键信息：
- `response.data.code` - 响应状态码
- `response.data.result` - 结果信息
- 是否包含任何URL相关的字段

### 3. 可能的成功响应格式
成功的响应可能包含：
```javascript
{
  "code": "OK",
  "result": "success",
  "url": "rtmp://...",
  // 或者
  "streamUrl": "http://...",
  // 或者
  "data": {
    "playUrl": "https://..."
  }
}
```

## 常见的摄像头JCP命令

### 海康威视系列
```javascript
{"command": "startLiveView", "channel": 1}
{"cmd": "live_start", "chn": 0}
```

### 大华系列
```javascript
{"method": "live.start", "params": {"channel": 0}}
{"cmd": "startRealPlay", "channel": 0}
```

### 萤石云系列
```javascript
{"heads": {"cmd": "liveAddress"}, "body": {"type": "rtmp"}}
{"action": "getLiveUrl", "channel": 0}
```

### 通用格式
```javascript
{"cmd": "video_start", "channel": 0}
{"action": "start_stream", "ch": 0}
{"method": "getStreamUrl", "params": {"channel": 0}}
```

## 下一步计划

### 如果找到有效命令
1. 提取视频流URL
2. 使用live-player播放
3. 添加播放控制功能

### 如果所有命令都失败
1. 分析设备的具体响应格式
2. 查找设备厂商的API文档
3. 考虑XP2P协议转换方案

### 可能需要的信息
1. 设备厂商和型号
2. 设备的API文档
3. 支持的JCP命令列表
4. XP2P SDK集成方案

## 调试技巧

### 1. 查看完整响应
```javascript
console.log('完整JCP响应:', JSON.stringify(response, null, 2));
```

### 2. 检查所有字段
```javascript
console.log('响应数据的所有字段:', Object.keys(response.data));
```

### 3. 寻找URL模式
在响应中寻找包含以下模式的字符串：
- `rtmp://`
- `http://` 或 `https://`
- `.m3u8`
- `.flv`
- `stream`、`live`、`video` 等关键词

请运行更新后的代码，并提供控制台中的详细输出，这样我们可以进一步分析和调整实现方案。
