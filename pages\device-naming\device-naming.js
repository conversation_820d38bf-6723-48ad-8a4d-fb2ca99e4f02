const app = getApp();
const deviceApi = require('../../api/device');
const { isLoggedIn, handleApiError } = require('../../utils/auth');

Page({
  data: {
    deviceInfo: {
      productKey: '',
      deviceName: '',
      snCode: '',
      mac: ''
    },
    deviceId: '', // 显示的设备ID
    customName: '', // 用户输入的自定义名称
    quickNames: ['厨房', '客厅', '卧室', '休息室', '庭院', '门口'], // 快捷命名选项
    selectedQuickName: '', // 选中的快捷命名
    groups: [], // 分组列表
    selectedGroupIndex: 0, // 选中的分组索引
    selectedGroupId: 0, // 选中的分组ID
    loading: false,
    showQRCode: false // 是否显示设备二维码
  },

  onLoad: function(options) {
    console.log('=== 设备命名页面加载 ===');
    console.log('页面参数:', options);

    // 从页面参数获取设备信息
    const { productKey, deviceName, snCode, mac } = options;

    if (!productKey || !deviceName) {
      wx.showModal({
        title: '参数错误',
        content: '设备信息不完整，请重新扫码',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    // 设置设备信息
    this.setData({
      deviceInfo: {
        productKey: productKey,
        deviceName: deviceName,
        snCode: snCode || deviceName,
        mac: mac || ''
      },
      deviceId: snCode || deviceName,
      customName: deviceName // 默认使用设备名称
    });

    // 加载分组列表
    this.loadGroupList();
  },

  /**
   * 加载分组列表
   */
  loadGroupList: function() {
    Promise.all([
      deviceApi.queryUserGroup(),
      deviceApi.queryUserDeviceList({ pageNo: 1, pageSize: 100 }) // 获取设备列表来统计数量
    ])
      .then(([groupResponse, deviceResponse]) => {
        // 处理分组数据
        const groups = [
          { groupId: 0, groupName: '默认分组', groupSort: 0 },
          ...(groupResponse && groupResponse.data ? groupResponse.data : [])
        ];

        // 统计每个分组的设备数量
        if (deviceResponse && deviceResponse.data && deviceResponse.data.data) {
          const devices = deviceResponse.data.data;
          const groupDeviceCount = {};

          // 初始化计数
          groups.forEach(group => {
            groupDeviceCount[group.groupId] = 0;
          });

          // 统计设备数量
          devices.forEach(device => {
            const groupId = device.groupId || 0;
            if (groupDeviceCount.hasOwnProperty(groupId)) {
              groupDeviceCount[groupId]++;
            }
          });

          // 添加设备数量信息
          groups.forEach(group => {
            group.deviceCount = groupDeviceCount[group.groupId] || 0;
          });
        } else {
          // 如果没有设备数据，设置默认数量为0
          groups.forEach(group => {
            group.deviceCount = 0;
          });
        }

        this.setData({
          groups: groups
        });
      })
      .catch(error => {
        console.error('获取分组列表失败:', error);
        // 如果获取失败，至少保证有默认分组
        this.setData({
          groups: [{ groupId: 0, groupName: '默认分组', groupSort: 0, deviceCount: 0 }]
        });
      });
  },

  /**
   * 自定义名称输入
   */
  onCustomNameInput: function(e) {
    const value = e.detail.value;
    this.setData({
      customName: value,
      selectedQuickName: '' // 清除快捷选择
    });
  },

  /**
   * 选择快捷命名
   */
  onSelectQuickName: function(e) {
    const name = e.currentTarget.dataset.name;
    this.setData({
      customName: name,
      selectedQuickName: name
    });
  },

  /**
   * 分组选择变化
   */
  onGroupChange: function(e) {
    const index = parseInt(e.detail.value);
    const group = this.data.groups[index];

    console.log('=== 分组选择变化 ===');
    console.log('选择索引:', index);
    console.log('选择分组:', group);

    this.setData({
      selectedGroupIndex: index,
      selectedGroupId: group ? group.groupId : 0
    });
  },

  /**
   * 添加分组
   */
  onAddGroup: function() {
    wx.showModal({
      title: '添加分组',
      content: '请输入分组名称',
      editable: true,
      placeholderText: '最多12个字符',
      success: (res) => {
        if (res.confirm && res.content) {
          const groupName = res.content.trim();
          this.createNewGroup(groupName);
        }
      }
    });
  },

  /**
   * 创建新分组
   */
  createNewGroup: function(groupName) {
    if (groupName.length > 12) {
      wx.showToast({
        title: '分组名称最多12个字符',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '创建分组中...'
    });

    // 计算新分组的排序值
    const maxSort = Math.max(...this.data.groups.map(g => g.groupSort || 0));
    const newSort = maxSort + 1;

    deviceApi.addUserGroup(groupName, newSort)
      .then(response => {
        wx.hideLoading();
        
        wx.showToast({
          title: '分组创建成功',
          icon: 'success'
        });

        // 重新加载分组列表
        this.loadGroupList();
      })
      .catch(error => {
        wx.hideLoading();
        console.error('创建分组失败:', error);
      });
  },

  /**
   * 显示设备二维码
   */
  onShowQRCode: function() {
    // 这里可以生成设备的二维码信息
    // 暂时显示设备信息
    const qrData = JSON.stringify({
      productKey: this.data.deviceInfo.productKey,
      deviceName: this.data.deviceInfo.deviceName,
      snCode: this.data.deviceInfo.snCode
    });

    wx.showModal({
      title: '设备二维码',
      content: `设备信息：\n${qrData}`,
      showCancel: false
    });
  },

  /**
   * 保存设备
   */
  onSaveDevice: function() {
    const { customName, deviceInfo, selectedGroupId } = this.data;

    // 验证设备名称
    if (!customName.trim()) {
      wx.showToast({
        title: '请输入设备名称',
        icon: 'none'
      });
      return;
    }

    if (customName.length > 16) {
      wx.showToast({
        title: '设备名称最多16个字符',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    wx.showLoading({
      title: '正在添加设备...'
    });

    console.log('=== 开始绑定设备 ===');
    console.log('设备信息:', deviceInfo);
    console.log('设备昵称:', customName);
    console.log('分组ID:', selectedGroupId);

    // 调用设备绑定API
    deviceApi.userBindDevice(
      deviceInfo.productKey,
      deviceInfo.deviceName,
      customName.trim(),
      selectedGroupId
    )
      .then(response => {
        console.log('=== 设备绑定成功 ===');
        console.log('响应数据:', response);

        wx.hideLoading();
        this.setData({ loading: false });

        if (response && response.data) {
          const bindResult = response.data.bind;
          
          if (bindResult === 200) {
            wx.showToast({
              title: '设备添加成功',
              icon: 'success'
            });
            this.navigateToDeviceList();
          } else if (bindResult === 1036) {
            wx.showModal({
              title: '设备状态',
              content: '设备已被您添加',
              showCancel: false,
              success: () => {
                this.navigateToDeviceList();
              }
            });
          } else if (bindResult === 1037) {
            const userName = response.data.userName || '其他用户';
            wx.showModal({
              title: '设备已被绑定',
              content: `该设备已被用户"${userName}"绑定`,
              showCancel: false
            });
          } else {
            wx.showToast({
              title: '设备添加成功',
              icon: 'success'
            });
            this.navigateToDeviceList();
          }
        } else {
          wx.showToast({
            title: '设备添加成功',
            icon: 'success'
          });
          this.navigateToDeviceList();
        }
      })
      .catch(error => {
        console.error('=== 设备绑定失败 ===');
        console.error('错误信息:', error);

        wx.hideLoading();
        this.setData({ loading: false });

        // 错误已在errorHandler中处理
      });
  },

  /**
   * 返回设备列表页面
   */
  navigateToDeviceList: function() {
    setTimeout(() => {
      // 返回到设备列表页面，并刷新列表
      const pages = getCurrentPages();
      if (pages.length >= 2) {
        const prevPage = pages[pages.length - 2];
        if (prevPage.route === 'pages/devices/devices') {
          // 如果上一页是设备列表页，刷新设备列表
          prevPage.loadDeviceList && prevPage.loadDeviceList();
        }
      }
      
      wx.navigateBack({
        delta: 2 // 返回到设备列表页面（跳过添加设备页面）
      });
    }, 1500);
  }
});
