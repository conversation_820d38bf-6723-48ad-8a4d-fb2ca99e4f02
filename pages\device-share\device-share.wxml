<view class="container">
  <view class="page-header">
    <text class="title">分享设备</text>
  </view>
  
  <view class="device-info">
    <image class="device-image" src="{{device.imageUrl || '/assets/images/device-default.png'}}"></image>
    <text class="device-name">{{device.name}}</text>
  </view>
  
  <view class="share-methods">
    <view class="method-title">选择分享方式</view>
    
    <view class="method-item" bindtap="shareByQrCode">
      <image src="/assets/images/qrcode.png" class="method-icon"></image>
      <view class="method-info">
        <text class="method-name">二维码分享</text>
        <text class="method-desc">生成二维码，扫码即可分享</text>
      </view>
      <image src="/assets/images/arrow-right.png" class="arrow-icon"></image>
    </view>
    
    <view class="method-item" bindtap="shareByContact">
      <image src="/assets/images/contact.png" class="method-icon"></image>
      <view class="method-info">
        <text class="method-name">好友分享</text>
        <text class="method-desc">选择好友直接分享</text>
      </view>
      <image src="/assets/images/arrow-right.png" class="arrow-icon"></image>
    </view>
  </view>
  
  <view class="shared-users">
    <view class="section-title">
      <text>已分享用户</text>
      <text class="user-count">({{sharedUsers.length}})</text>
    </view>
    
    <view class="user-list">
      <view class="user-item" wx:for="{{sharedUsers}}" wx:key="id">
        <image class="user-avatar" src="{{item.avatar || '/assets/images/default-avatar.png'}}"></image>
        <view class="user-info">
          <text class="user-name">{{item.name}}</text>
          <text class="share-time">分享时间: {{item.shareTime}}</text>
        </view>
        <view class="delete-btn" catchtap="cancelShare" data-id="{{item.id}}">
          <image src="/assets/images/delete.png" class="delete-icon"></image>
        </view>
      </view>
    </view>
    
    <view class="no-user" wx:if="{{sharedUsers.length === 0}}">
      <text>暂无分享用户</text>
    </view>
  </view>
</view> 