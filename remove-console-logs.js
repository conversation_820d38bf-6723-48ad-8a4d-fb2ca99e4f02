// 批量移除console.log的脚本
const fs = require('fs');
const path = require('path');

// 需要处理的文件列表
const filesToProcess = [
  'pages/devices/devices.js',
  'pages/device-detail/device-detail.js', 
  'pages/device-alarm/device-alarm.js',
  'pages/register/register.js',
  'utils/auth.js',
  'utils/crypto.js',
  'utils/errorHandler.js',
  'utils/request.js'
];

// 移除console.log的正则表达式
const consoleRegex = /^\s*console\.(log|error|warn|info|debug)\([^)]*\);\s*$/gm;
const multiLineConsoleRegex = /^\s*console\.(log|error|warn|info|debug)\([^)]*$/gm;

function removeConsoleLogs(filePath) {
  try {
    const fullPath = path.resolve(filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`文件不存在: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(fullPath, 'utf8');
    const originalLength = content.length;
    
    // 移除单行console语句
    content = content.replace(consoleRegex, '');
    
    // 移除多行console语句（简单处理）
    const lines = content.split('\n');
    const filteredLines = [];
    let skipNext = false;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // 检查是否是console语句的开始
      if (/^\s*console\.(log|error|warn|info|debug)\s*\(/.test(line)) {
        // 检查是否在同一行结束
        if (line.includes(');')) {
          // 单行console语句，跳过
          continue;
        } else {
          // 多行console语句开始，标记跳过后续行
          skipNext = true;
          continue;
        }
      }
      
      // 如果在跳过模式中，检查是否结束
      if (skipNext) {
        if (line.includes(');')) {
          skipNext = false;
        }
        continue;
      }
      
      filteredLines.push(line);
    }
    
    content = filteredLines.join('\n');
    
    if (content.length !== originalLength) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`已处理: ${filePath} (${originalLength} -> ${content.length} 字符)`);
    } else {
      console.log(`无需处理: ${filePath}`);
    }
    
  } catch (error) {
    console.error(`处理文件失败 ${filePath}:`, error.message);
  }
}

// 处理所有文件
console.log('开始批量移除console.log...');
filesToProcess.forEach(removeConsoleLogs);
console.log('处理完成！');
