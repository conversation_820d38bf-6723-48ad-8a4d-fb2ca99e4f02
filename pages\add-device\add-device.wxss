/* pages/add-device/add-device.wxss */
page {
  background-color: #f7f9fc;
  height: 100%;
  --primary-color: #36b9ff;
}

/* 导航栏自定义样式 */
.custom-nav {
  background-color: var(--primary-color) !important;
  background: linear-gradient(135deg, #36b9ff 0%, #3652ff 100%) !important;
}

.nav-title {
  color: white !important;
  font-weight: 500 !important;
}

.container {
  padding: 30rpx;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 20rpx;
}

.header-desc {
  font-size: 28rpx;
  color: #666;
}

/* 添加方式选择容器 - 新的卡片式布局 */
.add-methods-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

/* 添加方式卡片 */
.method-card {
  width: 30%;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s;
}

.method-card:active {
  transform: scale(0.97);
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.1);
}

.method-icon {
  margin-bottom: 20rpx;
}

.icon-bg {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #36b9ff 0%, #3652ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  font-size: 50rpx;
  color: white;
}

.icon-scan::before {
  content: "📷";
}

.icon-edit::before {
  content: "📝";
}

.icon-bluetooth::before {
  content: "📶";
}

.method-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.method-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

/* 分组选择器 */
.group-selector {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
}

.selector-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.group-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.picker-text {
  font-size: 30rpx;
  color: #495057;
}

.picker-arrow {
  font-size: 24rpx;
  color: #6c757d;
  transform: rotate(90deg);
}

/* 帮助说明 */
.help-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
}

.help-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.help-content {
  /* 内容样式 */
}

.help-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #36b9ff;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.help-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  min-width: 200rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #36b9ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
