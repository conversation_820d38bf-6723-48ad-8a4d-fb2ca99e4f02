# 小程序设备功能改进说明

## 概述
根据小程序接口文档，对设备添加、二维码添加和设备详情功能进行了全面改进，确保所有API调用都使用正确的参数格式和字段。

## 主要改进内容

### 1. 完善设备API (`api/device.js`)

#### 新增API函数：
- **`getDeviceInfoBySN(snCode)`** - 根据SN查询设备信息
  - 接口：`GET_DEVICE_INFO_BY_SN`
  - 参数：`snCode` (设备SN码)
  - 返回：设备的productKey、deviceName、mac等信息

- **`getUserBindDeviceToken(groupId)`** - 获取用户设备扫码TOKEN
  - 接口：`GET_USER_BIND_DEVICE_TOKEN`
  - 参数：`groupId` (分组ID，可选，默认为0)
  - 返回：用于二维码绑定的token

- **`getTokenBindDeviceInfo(token)`** - 获取TOKEN绑定设备信息
  - 接口：`GET_TOKEN_BIND_DEVICE_INFO`
  - 参数：`token` (用户TOKEN)
  - 返回：设备信息和绑定状态

### 2. 改进设备列表页面 (`pages/devices/devices.js`)

#### 主要修改：
- **真实API调用**：将`getDeviceInfoBySN`方法从模拟数据改为调用真实API
- **完善设备绑定**：`bindDeviceWithInfo`方法现在使用完整的参数（productKey, deviceName, nickName, groupId）
- **增强二维码处理**：
  - 支持TOKEN格式的二维码识别和处理
  - 添加`isTokenQRCode`方法判断二维码类型
  - 添加`processTokenQRCode`方法处理TOKEN类型二维码
  - 根据绑定状态显示不同的提示信息

#### 二维码格式支持：
1. **JSON格式**：`{"productKey":"xxx","deviceName":"xxx"}`
2. **序列号格式**：直接的设备序列号字符串
3. **TOKEN格式**：32位十六进制字符串
4. **URL格式**：`https://platform.cnjabsco.com/a/d?sncode=设备序列号`

#### 绑定状态处理：
- `200` - 绑定成功
- `1036` - 设备已被自己添加
- `1037` - 设备已被别人添加（显示绑定用户名）

### 3. 完善设备详情页面 (`pages/device-detail/device-detail.js`)

#### 主要修改：
- **真实解绑API**：`unbindDevice`方法现在调用真实的`UNBIND_USER_DEVICE`接口
- **参数完整性**：确保所有API调用都使用正确的productKey和deviceName参数

### 4. 新增专门的添加设备页面 (`pages/add-device/`)

#### 功能特点：
- **统一的添加入口**：集成扫码、手动输入、蓝牙连接等多种添加方式
- **分组选择**：支持选择设备要添加到的分组
- **完整的二维码支持**：
  - JSON格式二维码（包含productKey和deviceName）
  - 序列号格式二维码
  - TOKEN格式二维码（32位十六进制字符串）
  - URL格式二维码（包含sncode参数的URL）
- **用户友好界面**：
  - 清晰的操作指引
  - 帮助说明和常见问题解答
  - 加载状态和错误处理

#### 文件结构：
- `add-device.js` - 页面逻辑
- `add-device.wxml` - 页面结构
- `add-device.wxss` - 页面样式
- `add-device.json` - 页面配置

### 5. 新增设备命名页面 (`pages/device-naming/`)

#### 功能特点：
- **设备信息展示**：显示扫码获取的设备ID
- **自定义命名**：支持手动输入设备名称（最多16个字符）
- **快捷命名**：提供常用的设备位置名称（厨房、客厅、卧室等）
- **分组管理**：
  - 选择现有分组
  - 创建新分组（最多12个字符）
  - 显示每个分组的设备数量
- **设备二维码**：提供设备二维码的保存功能
- **完整的绑定流程**：调用真实API完成设备绑定

#### 页面流程：
1. 从添加设备页面扫码成功后跳转到此页面
2. 显示设备ID和基本信息
3. 用户输入或选择设备名称
4. 选择设备分组（可创建新分组）
5. 点击保存完成设备绑定
6. 返回设备列表页面并刷新

#### 文件结构：
- `device-naming.js` - 页面逻辑
- `device-naming.wxml` - 页面结构
- `device-naming.wxss` - 页面样式
- `device-naming.json` - 页面配置

### 6. 更新应用配置 (`app.json`)
- 注册新的添加设备页面路径
- 注册新的设备命名页面路径

## API调用规范

### 请求格式
根据接口文档，所有API调用都遵循以下格式：

#### 请求头 (Header)：
```javascript
{
  "Signature": JSON.stringify({
    "appKey": "2000008",
    "timestamp": 1741765761000,
    "nonce": "1d4gge32",
    "sign": "签名值",
    "clientId": "设备ID"
  }),
  "Token": "登录token",
  "Content-Type": "application/json"
}
```

#### 请求体 (Body)：
```javascript
{
  "Request": {
    "requestId": "UUID",
    "appPackageName": "com.applet.shankouhouse",
    "appVersion": "1.0.0",
    "phoneVersion": "系统版本",
    "phoneModel": "手机型号",
    "appLanguage": "zh_CN",
    "network": "wifi",
    "country": "CN"
  },
  "Params": {
    "Action": "接口动作",
    // 其他业务参数
  }
}
```

### 关键接口参数

#### 用户绑定设备 (`USER_BIND_DEVICE`)：
- `productKey` (String, 必需) - 产品ID
- `deviceName` (String, 必需) - 设备名称
- `nickName` (String, 可选) - 设备昵称
- `groupId` (Long, 必需) - 分组ID，默认分组传0

#### 根据SN查询设备信息 (`GET_DEVICE_INFO_BY_SN`)：
- `snCode` (String, 必需) - 设备SN码

#### 获取用户设备扫码TOKEN (`GET_USER_BIND_DEVICE_TOKEN`)：
- `groupId` (Integer, 可选) - 分组ID，不传按默认分组(0)处理

#### 获取TOKEN绑定设备信息 (`GET_TOKEN_BIND_DEVICE_INFO`)：
- `token` (String, 必需) - 用户TOKEN

## 使用说明

### 添加设备流程：
1. 用户点击"添加设备"按钮
2. 跳转到专门的添加设备页面
3. 选择添加方式：
   - **扫描二维码**：支持JSON、序列号、TOKEN三种格式
   - **手动输入**：输入设备序列号
   - **蓝牙连接**：（功能开发中）
4. 扫码或输入成功后跳转到设备命名页面
5. 在设备命名页面：
   - 查看设备ID信息
   - 输入或选择设备名称
   - 选择设备分组（可创建新分组）
   - 点击保存完成绑定
6. 系统调用API完成设备绑定
7. 返回设备列表页面并刷新

### 设备详情功能：
- 查看设备状态（在线/离线）
- 获取设备属性信息
- 修改设备昵称
- 解绑设备
- 查看设备告警信息

## URL格式二维码处理

### 新增功能
为了支持实际使用中的URL格式二维码（如：`https://platform.cnjabsco.com/a/d?sncode=395ce24901b8553c981c1004`），新增了URL格式的二维码识别和处理功能。

### 处理流程
1. **URL识别**：检查二维码内容是否以`http://`或`https://`开头，且包含`sncode=`参数
2. **参数提取**：使用正则表达式解析URL，提取`sncode`参数值（兼容微信小程序环境）
3. **格式验证**：验证提取的序列号格式（8位以上字母数字组合）
4. **设备查询**：调用`GET_DEVICE_INFO_BY_SN`接口查询设备信息
5. **设备绑定**：跳转到设备命名页面或直接绑定设备

### 支持的URL格式
- `https://platform.cnjabsco.com/a/d?sncode=设备序列号`
- `http://platform.cnjabsco.com/a/d?sncode=设备序列号`
- 其他包含`sncode`参数的URL格式

### 技术实现
- **URL工具函数**：创建了`utils/urlUtils.js`工具文件，统一处理URL解析
- **正则表达式解析**：使用`/[?&]sncode=([^&]+)/`正则表达式提取参数，兼容微信小程序环境
- **参数解码**：使用`decodeURIComponent`处理URL编码的参数值
- **错误处理**：完善的错误捕获和用户提示机制

### 错误处理
- URL解析失败：显示"无法从二维码URL中提取有效的设备序列号"
- 缺少sncode参数：显示"URL中未找到设备序列号"
- 序列号格式错误：显示"设备序列号格式不正确"
- 设备不存在：显示"未找到该二维码对应的设备"

## 问题修复记录

### 修复URL解析问题
- **问题**：微信小程序环境不支持`URL`构造函数，导致URL解析失败
- **解决方案**：使用正则表达式`/[?&]sncode=([^&]+)/`替代`URL`对象进行参数提取
- **影响文件**：`pages/add-device/add-device.js`, `pages/devices/devices.js`, `utils/urlUtils.js`

### 修复参数验证问题
- **问题**：`validateRequiredFields`函数调用缺少第二个参数`requiredFields`，导致`filter`方法在`undefined`上调用失败
- **解决方案**：为所有`validateRequiredFields`调用添加正确的字段数组参数，并统一使用`validation.valid`属性
- **影响文件**：`api/device.js`中的所有API函数

## 注意事项

1. **登录验证**：所有设备相关操作都需要用户登录
2. **参数验证**：严格按照接口文档要求传递参数，使用正确的验证函数调用格式
3. **错误处理**：统一使用errorHandler处理API错误
4. **用户体验**：提供清晰的加载状态和错误提示
5. **数据格式**：Request和Params必须作为对象发送，不是JSON字符串
6. **二维码兼容性**：支持多种二维码格式，优先级为URL > TOKEN > JSON > 序列号
7. **环境兼容性**：考虑微信小程序环境的API限制，使用兼容的替代方案

## 测试建议

### 1. 添加设备页面测试
- **页面跳转**：从设备列表点击"添加设备"按钮
- **扫码功能**：测试不同格式的二维码
- **手动输入**：测试序列号输入和验证
- **界面交互**：测试帮助说明和常见问题

### 2. 设备命名页面测试
- **页面参数**：确保从添加设备页面正确传递设备信息
- **设备信息显示**：验证设备ID正确显示
- **命名功能**：
  - 测试自定义输入（最多16个字符）
  - 测试快捷命名选择
  - 测试名称验证逻辑
- **分组功能**：
  - 测试分组列表加载
  - 测试分组选择
  - 测试创建新分组（最多12个字符）
  - 测试设备数量统计显示
- **保存功能**：测试设备绑定API调用

### 3. 二维码测试
- **JSON格式**：`{"productKey":"xxx","deviceName":"xxx"}`
- **序列号格式**：8位以上字母数字组合
- **TOKEN格式**：32位十六进制字符串（如：`a1b2c3d4e5f6789012345678901234ab`）
- **URL格式**：`https://platform.cnjabsco.com/a/d?sncode=395ce24901b8553c981c1004`

