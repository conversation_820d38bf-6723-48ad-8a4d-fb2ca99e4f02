<view class="container">
  <view class="page-header">
    <text class="title">报警消息</text>
    <view class="filter-btn" bindtap="showFilterOptions">
      <van-icon name="filter-o" size="20px" color="#666666" />
    </view>
  </view>
  
  <view class="date-filter">
    <picker mode="date" value="{{startDate}}" start="2020-01-01" end="{{endDate}}" bindchange="onStartDateChange">
      <view class="date-item">
        <text class="date-label">开始日期：</text>
        <text class="date-value">{{startDate}}</text>
      </view>
    </picker>
    <view class="date-separator"></view>
    <picker mode="date" value="{{endDate}}" start="{{startDate}}" end="{{today}}" bindchange="onEndDateChange">
      <view class="date-item">
        <text class="date-label">结束日期：</text>
        <text class="date-value">{{endDate}}</text>
      </view>
    </picker>
  </view>
  
  <view class="alarm-list">
    <view class="date-group" wx:for="{{alarmGroups}}" wx:key="date">
      <view class="date-header">
        <text class="date-text">{{item.date}}</text>
        <text class="count">({{item.alarms.length}})</text>
      </view>
      
      <view class="alarm-item" wx:for="{{item.alarms}}" wx:for-item="alarm" wx:key="id" bindtap="viewAlarmDetail" data-id="{{alarm.id}}">
        <view class="alarm-time">{{alarm.time}}</view>
        <view class="alarm-content">
          <view class="alarm-type">
            <van-icon name="{{alarm.typeIcon}}" size="16px" color="#FF6B35" class="type-icon" />
            <text class="type-name">{{alarm.typeName}}</text>
          </view>
          <text class="alarm-desc">{{alarm.description}}</text>
        </view>
      </view>
    </view>
  </view>
  
  <view class="no-data" wx:if="{{alarmGroups.length === 0 && !loading}}">
    <van-icon name="warning-o" size="60px" color="#CCCCCC" class="no-data-icon" />
    <text class="no-data-text">暂无报警消息</text>
  </view>
  
  <view class="bottom-bar">
    <button class="btn-clear-all" bindtap="confirmClearAll">清空报警消息</button>
  </view>
</view> 