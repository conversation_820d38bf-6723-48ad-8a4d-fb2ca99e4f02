/* 页面基础样式 */
.page {
  background-color: #f7f8fa;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 内容容器 */
.container {
  padding: 16px;
}

/* 设备信息卡片 */
.info-card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-id {
  font-size: 12px;
  color: #969799;
}

.device-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.device-status.online {
  background-color: rgba(7, 193, 96, 0.1);
  color: #07c160;
}

.device-status.offline {
  background-color: rgba(238, 10, 36, 0.1);
  color: #ee0a24;
}

.card-content {
  font-size: 14px;
}

.info-row {
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.info-label {
  color: #969799;
  font-size: 14px;
}

.info-value {
  color: #323233;
  font-weight: 500;
  font-size: 14px;
  text-align: right;
  flex: 1;
  margin-left: 16px;
  word-break: break-all;
}

/* 功能菜单 */
.function-menu {
  background-color: #ffffff;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.menu-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  padding: 16px 16px 0 16px;
  margin-bottom: 8px;
}

.menu-items {
  padding: 0 16px 16px 16px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f7f8fa;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item text {
  flex: 1;
  margin-left: 12px;
  font-size: 14px;
  color: #323233;
}

/* 设备属性卡片 */
.attributes-card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
}

.attributes-content {
  max-height: 300px;
  overflow-y: auto;
}

.attr-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #f7f8fa;
}

.attr-item:last-child {
  border-bottom: none;
}

.attr-key {
  font-size: 12px;
  color: #969799;
  width: 40%;
  word-break: break-all;
}

.attr-value {
  font-size: 12px;
  color: #323233;
  text-align: right;
  width: 55%;
  word-break: break-all;
}

/* 底部操作区 */
.bottom-actions {
  margin-top: 20px;
  padding-bottom: 20px;
}
