const app = getApp();
const { queryDeviceStatus, updateDeviceNickName, getDeviceXp2pInfo, getDeviceThingAttribute, unbindUserDevice, jcpInteraction } = require('../../api/device.js');

Page({
  data: {
    device: {
      productKey: '',
      deviceName: '',
      nickName: '智能摄像头',
      status: -1, // -1:查询失败，0：未激活，1：在线，2：离线
      xp2pInfo: '',
      attributes: {},
      // 从属性中解析的信息
      cloudStorageDays: 0,
      cloudStorageEnabled: false,
      supportPtz: false
    },
    loading: true,
    refreshing: false,

    // 视频播放相关
    isPlaying: false,
    videoUrl: '',
    isMuted: false
  },

  onLoad: function(options) {
    const { productKey, deviceName, nickName } = options;

    if (!productKey || !deviceName) {
      wx.showToast({
        title: '设备信息不完整',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    this.setData({
      'device.productKey': productKey,
      'device.deviceName': deviceName,
      'device.nickName': nickName || deviceName
    });

    // 加载设备详情信息
    this.loadDeviceInfo();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.setData({ refreshing: true });
    this.loadDeviceInfo().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 返回按钮点击处理
   */
  onBackClick: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 加载设备信息
   */
  loadDeviceInfo: function() {
    const device = this.data.device;

    this.setData({ loading: true });

    // 并行加载设备信息
    const promises = [
      this.loadDeviceStatus(),
      this.loadDeviceXp2pInfo(),
      this.loadDeviceAttributes()
    ];

    return Promise.allSettled(promises)
      .then(results => {
        console.log('设备信息加载完成:', results);
        this.setData({ loading: false });

        // 如果设备在线，尝试获取视频流
        if (this.data.device.status === 1) {
          this.tryGetVideoStream();
        }
      })
      .catch(error => {
        console.error('加载设备信息失败:', error);
        this.setData({ loading: false });
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  /**
   * 加载设备状态
   */
  loadDeviceStatus: function() {
    const { productKey, deviceName } = this.data.device;

    return queryDeviceStatus(productKey, deviceName)
      .then(response => {
        if (response && response.code === 200 && response.data) {
          console.log('设备状态响应:', response.data);

          // 根据接口文档，status字段：-1:查询失败，0：未激活，1：在线，2：离线
          const status = response.data.status;

          this.setData({
            'device.status': status
          });
        }
      })
      .catch(error => {
        console.error('获取设备状态失败:', error);
        this.setData({
          'device.status': -1
        });
      });
  },

  /**
   * 加载设备XP2P信息
   */
  loadDeviceXp2pInfo: function() {
    const { productKey, deviceName } = this.data.device;

    return getDeviceXp2pInfo(productKey, deviceName)
      .then(response => {
        console.log('获取XP2P信息响应:', response);

        if (response && response.code === 200 && response.data) {
          // 根据接口文档，XP2P信息在 data.value 字段中
          const xp2pInfo = response.data.value || response.data.Value || '';

          if (xp2pInfo) {
            console.log('获取到XP2P信息:', xp2pInfo);
            this.setData({ 'device.xp2pInfo': xp2pInfo });
          }
        }
      })
      .catch(error => {
        console.error('获取XP2P信息失败:', error);
      });
  },

  /**
   * 加载设备属性
   */
  loadDeviceAttributes: function() {
    const { productKey, deviceName } = this.data.device;

    return getDeviceThingAttribute(productKey, deviceName)
      .then(response => {
        console.log('获取设备属性响应:', response);

        if (response && response.code === 200 && response.data) {
          const attributes = response.data;

          // 根据接口文档解析常见的设备属性
          const cloudStorageDays = attributes._sys_cs_days?.Value || 0;
          const cloudStorageStatus = attributes._sys_cs_status?.Value || 0;
          const supportPtz = attributes._sys_support_ptz?.Value || 0;

          this.setData({
            'device.attributes': attributes,
            'device.cloudStorageDays': cloudStorageDays,
            'device.cloudStorageEnabled': cloudStorageStatus === 1,
            'device.supportPtz': supportPtz === 1
          });
        }
      })
      .catch(error => {
        console.error('获取设备属性失败:', error);
      });
  },

  /**
   * 获取状态文本
   */
  getStatusText: function(status) {
    const statusMap = {
      '-1': '查询失败',
      '0': '未激活',
      '1': '在线',
      '2': '离线'
    };

    return statusMap[status] || '未知状态';
  },

  showEditName: function() {
    const that = this;
    wx.showModal({
      title: '修改设备昵称',
      content: '',
      editable: true,
      placeholderText: that.data.device.nickName,
      success (res) {
        if (res.confirm) {
          const newName = res.content.trim();
          if (newName && newName !== that.data.device.nickName) {
            that.updateDeviceNickName(newName);
          }
        }
      }
    });
  },

  updateDeviceNickName: function(newName) {
    const { productKey, deviceName } = this.data.device;

    wx.showLoading({
      title: '更新中...'
    });

    updateDeviceNickName(productKey, deviceName, newName)
      .then(response => {
        wx.hideLoading();

        this.setData({
          'device.nickName': newName
        });

        wx.showToast({
          title: '设备昵称已更新',
          icon: 'success'
        });
      })
      .catch(error => {
        wx.hideLoading();

        // 错误已在errorHandler中处理，这里不需要额外显示
      });
  },

  /**
   * 尝试获取视频流
   */
  tryGetVideoStream: function() {
    const { productKey, deviceName } = this.data.device;

    console.log('尝试获取视频流...');

    // 根据接口文档，使用JCP交互获取视频流
    // 使用标准的JCP命令格式
    const jcpCmd = JSON.stringify({
      "cmd": "startRealPlay",
      "channel": 0,
      "streamType": 0
    });

    console.log('发送JCP命令:', jcpCmd);

    jcpInteraction(productKey, deviceName, jcpCmd)
      .then(response => {
        console.log('JCP响应:', response);
        console.log('JCP响应数据详情:', JSON.stringify(response.data, null, 2));

        if (response && response.code === 200) {
          if (response.data && typeof response.data === 'object') {
            // 检查响应中是否包含视频流URL
            const streamUrl = this.extractStreamUrl(response.data);

            if (streamUrl) {
              console.log('获取到视频流URL:', streamUrl);
              this.setData({
                videoUrl: streamUrl,
                isPlaying: true
              });
              return;
            } else {
              console.log('响应数据中没有找到有效的视频流URL');
              console.log('响应数据结构:', Object.keys(response.data));
            }
          } else {
            console.log('响应数据为空或格式不正确');
          }

          // 如果命令不被识别，尝试其他命令格式
          if (response.data && response.data.result &&
              response.data.result.includes('no such command')) {
            console.log('当前命令不被识别，尝试其他命令格式');
            this.tryAlternativeCommands();
          } else {
            // 如果有XP2P信息，显示说明
            if (this.data.device.xp2pInfo) {
              console.log('设备有XP2P信息，但JCP命令未返回标准视频流URL');
              this.showVideoStreamNotice();
            }
          }
        }
      })
      .catch(error => {
        console.error('JCP命令失败:', error);
        wx.showToast({
          title: 'JCP命令执行失败',
          icon: 'none'
        });
      });
  },

  /**
   * 尝试其他JCP命令格式
   */
  tryAlternativeCommands: function() {
    const { productKey, deviceName } = this.data.device;

    // 常见的JCP命令格式列表
    const alternativeCommands = [
      // 标准格式
      JSON.stringify({"cmd": "startRealPlay", "channel": 0}),
      JSON.stringify({"action": "getLiveStream", "channel": 0}),
      JSON.stringify({"method": "live.start", "params": {"channel": 0}}),

      // 简化格式
      JSON.stringify({"type": "live", "channel": 0}),
      JSON.stringify({"command": "startLive", "channelId": 0}),

      // 厂商特定格式
      JSON.stringify({"cmd": "video_start", "chn": 0, "stream": 0}),
      JSON.stringify({"action": "start_preview", "ch": 0}),
      JSON.stringify({"method": "startPreview", "channel": 0, "quality": "HD"})
    ];

    this.tryCommandList(alternativeCommands, 0);
  },

  /**
   * 依次尝试命令列表
   */
  tryCommandList: function(commands, index) {
    if (index >= commands.length) {
      console.log('所有备选JCP命令都尝试失败');
      if (this.data.device.xp2pInfo) {
        this.showVideoStreamNotice();
      }
      return;
    }

    const { productKey, deviceName } = this.data.device;
    const jcpCmd = commands[index];

    console.log(`尝试备选命令 ${index + 1}:`, jcpCmd);

    jcpInteraction(productKey, deviceName, jcpCmd)
      .then(response => {
        console.log(`备选命令 ${index + 1} 响应:`, response);
        console.log(`备选命令 ${index + 1} 响应数据:`, JSON.stringify(response.data, null, 2));

        if (response && response.code === 200 && response.data) {
          // 检查是否还是"no such command"错误
          if (response.data.result && response.data.result.includes('no such command')) {
            console.log(`备选命令 ${index + 1} 也不被识别，继续尝试下一个`);
            this.tryCommandList(commands, index + 1);
            return;
          }

          // 检查响应中是否包含视频流URL
          const streamUrl = this.extractStreamUrl(response.data);

          if (streamUrl) {
            console.log('通过备选命令获取到视频流URL:', streamUrl);
            this.setData({
              videoUrl: streamUrl,
              isPlaying: true
            });
            return;
          }
        }

        // 当前命令没有返回有效URL，尝试下一个
        this.tryCommandList(commands, index + 1);
      })
      .catch(error => {
        console.error(`备选命令 ${index + 1} 失败:`, error);
        // 当前命令失败，尝试下一个
        this.tryCommandList(commands, index + 1);
      });
  },

  /**
   * 显示视频流说明
   */
  showVideoStreamNotice: function() {
    const device = this.data.device;

    wx.showModal({
      title: '视频流获取说明',
      content: `设备状态：${this.getStatusText(device.status)}
XP2P信息：${device.xp2pInfo ? '已获取' : '未获取'}

JCP命令已成功执行，但未返回标准的视频流URL。
这可能需要：
1. 服务器端支持XP2P协议转换
2. 使用设备厂商提供的专用SDK
3. 或者设备不支持直接的视频流输出

请联系技术支持获取更多帮助。`,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 从响应中提取视频流URL
   */
  extractStreamUrl: function(data) {
    console.log('尝试从响应中提取视频流URL:', data);

    // 如果data直接是字符串且是URL
    if (typeof data === 'string' && this.isValidStreamUrl(data)) {
      console.log('找到字符串格式的URL:', data);
      return data;
    }

    // 如果data是对象，检查常见的URL字段
    if (typeof data === 'object' && data !== null) {
      // 扩展更多可能的URL字段名
      const urlFields = [
        'url', 'streamUrl', 'videoUrl', 'playUrl', 'liveUrl',
        'rtmpUrl', 'hlsUrl', 'flvUrl', 'webrtcUrl',
        'stream_url', 'video_url', 'play_url', 'live_url',
        'address', 'addr', 'link', 'src'
      ];

      for (const field of urlFields) {
        if (data[field] && typeof data[field] === 'string' && this.isValidStreamUrl(data[field])) {
          console.log(`找到${field}字段的URL:`, data[field]);
          return data[field];
        }
      }

      // 递归检查嵌套对象
      for (const key in data) {
        if (typeof data[key] === 'object' && data[key] !== null) {
          const nestedUrl = this.extractStreamUrl(data[key]);
          if (nestedUrl) {
            console.log(`在${key}嵌套对象中找到URL:`, nestedUrl);
            return nestedUrl;
          }
        }
      }
    }

    console.log('未找到有效的视频流URL');
    return null;
  },

  /**
   * 检查是否是有效的流媒体URL
   */
  isValidStreamUrl: function(url) {
    if (!url || typeof url !== 'string') {
      return false;
    }

    // 检查是否是支持的流媒体协议
    const supportedProtocols = [
      'rtmp://', 'rtmps://',
      'http://', 'https://',
      'ws://', 'wss://',
      'rtsp://', 'rtsps://',
      'flv://', 'hls://'
    ];

    const isValidProtocol = supportedProtocols.some(protocol =>
      url.toLowerCase().startsWith(protocol)
    );

    // 检查是否包含常见的流媒体文件扩展名
    const streamExtensions = ['.m3u8', '.flv', '.mp4', '.ts'];
    const hasStreamExtension = streamExtensions.some(ext =>
      url.toLowerCase().includes(ext)
    );

    console.log(`URL验证: ${url}, 协议有效: ${isValidProtocol}, 扩展名有效: ${hasStreamExtension}`);

    return isValidProtocol || hasStreamExtension;
  },

  /**
   * 开始/停止播放
   */
  togglePlay: function() {
    if (this.data.isPlaying) {
      this.stopPlay();
    } else {
      this.startPlay();
    }
  },

  /**
   * 开始播放
   */
  startPlay: function() {
    if (this.data.device.status !== 1) {
      wx.showToast({
        title: '设备离线，无法播放',
        icon: 'none'
      });
      return;
    }

    if (!this.data.videoUrl) {
      // 重新尝试获取视频流
      this.tryGetVideoStream();
    } else {
      this.setData({ isPlaying: true });
    }
  },

  /**
   * 停止播放
   */
  stopPlay: function() {
    this.setData({ isPlaying: false });
  },

  /**
   * 切换静音
   */
  toggleMute: function() {
    this.setData({
      isMuted: !this.data.isMuted
    });
  },

  /**
   * 播放器状态变化
   */
  onPlayerStateChange: function(e) {
    console.log('播放器状态变化:', e.detail);
    const { code } = e.detail;

    switch (code) {
      case 2001: // 已连接服务器
        console.log('已连接服务器');
        break;
      case 2002: // 已连接服务器,开始拉流
        console.log('开始拉流');
        break;
      case 2003: // 网络接收到首个视频数据包(IDR)
        console.log('接收到首个视频数据包');
        break;
      case 2004: // 视频播放开始
        console.log('视频播放开始');
        break;
      case -2301: // 网络断连，且经多次重连抢救无效，更多重试请自行重启播放
        console.log('网络断连');
        wx.showToast({
          title: '网络连接断开',
          icon: 'none'
        });
        break;
    }
  },

  /**
   * 播放器错误
   */
  onPlayerError: function(e) {
    console.error('播放器错误:', e.detail);
    wx.showToast({
      title: '播放失败',
      icon: 'none'
    });

    this.setData({
      isPlaying: false
    });
  },

  /**
   * 获取设备状态文本
   */
  getStatusText: function(status) {
    const statusMap = {
      '-1': '查询失败',
      '0': '未激活',
      '1': '在线',
      '2': '离线'
    };
    return statusMap[status] || '未知状态';
  },

  /**
   * 获取设备状态样式类
   */
  getStatusClass: function(status) {
    return status === 1 ? 'online' : 'offline';
  },

  /**
   * 跳转到报警消息页面
   */
  goToAlarm: function() {
    const { productKey, deviceName, nickName } = this.data.device;
    wx.navigateTo({
      url: `/pages/device-alarm/device-alarm?productKey=${productKey}&deviceName=${deviceName}&nickName=${nickName}`
    });
  },

  /**
   * 跳转到设备分享页面
   */
  shareDevice: function() {
    const { productKey, deviceName, nickName } = this.data.device;
    wx.navigateTo({
      url: `/pages/device-share/device-share?productKey=${productKey}&deviceName=${deviceName}&nickName=${nickName}`
    });
  },

  /**
   * 确认解绑设备
   */
  confirmUnbindDevice: function() {
    wx.showModal({
      title: '确认解绑',
      content: '解绑后将无法控制此设备，确定要解绑吗？',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.unbindDevice();
        }
      }
    });
  },

  /**
   * 解绑设备
   */
  unbindDevice: function() {
    const { productKey, deviceName } = this.data.device;

    wx.showLoading({
      title: '解绑中...'
    });

    unbindUserDevice(productKey, deviceName)
      .then(response => {
        wx.hideLoading();
        wx.showToast({
          title: '设备解绑成功',
          icon: 'success'
        });

        // 返回设备列表页面
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      })
      .catch(error => {
        wx.hideLoading();
        wx.showToast({
          title: '解绑失败: ' + (error.msg || '未知错误'),
          icon: 'none'
        });
      });
  }

});